{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/providers/ToastProvider.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Toaster } from 'react-hot-toast';\r\n\r\nexport default function ToastProvider() {\r\n    return (\r\n        <Toaster\r\n            position=\"top-right\"\r\n            toastOptions={{\r\n                duration: 4000,\r\n                style: {\r\n                    background: '#363636',\r\n                    color: '#fff',\r\n                    fontSize: '14px',\r\n                    borderRadius: '8px',\r\n                    padding: '12px 16px',\r\n                    boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',\r\n                },\r\n            }}\r\n        />\r\n    );\r\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACpB,qBACI,8OAAC,uJAAA,CAAA,UAAO;QACJ,UAAS;QACT,cAAc;YACV,UAAU;YACV,OAAO;gBACH,YAAY;gBACZ,OAAO;gBACP,UAAU;gBACV,cAAc;gBACd,SAAS;gBACT,WAAW;YACf;QACJ;;;;;;AAGZ", "debugId": null}}, {"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/redux/slice/authSlice.ts"], "sourcesContent": ["import { createSlice } from '@reduxjs/toolkit';\r\n\r\ninterface AuthState {\r\n    isLoggedIn: boolean;\r\n    user: any | null;\r\n}\r\n\r\n// Initialize state from localStorage if available\r\nconst getInitialState = (): AuthState => {\r\n    if (typeof window !== 'undefined') {\r\n        const token = localStorage.getItem('accessToken');\r\n        return {\r\n            isLoggedIn: !!token,\r\n            user: null,\r\n        };\r\n    }\r\n    return {\r\n        isLoggedIn: false,\r\n        user: null,\r\n    };\r\n};\r\n\r\nconst initialState: AuthState = getInitialState();\r\n\r\nconst authSlice = createSlice({\r\n    name: 'auth',\r\n    initialState,\r\n    reducers: {\r\n        login: (state, action) => {\r\n            state.isLoggedIn = true;\r\n            state.user = action.payload || null;\r\n        },\r\n        logout: (state) => {\r\n            state.isLoggedIn = false;\r\n            state.user = null;\r\n        },\r\n        setUser: (state, action) => {\r\n            state.user = action.payload;\r\n        },\r\n    },\r\n});\r\n\r\nexport const { login, logout, setUser } = authSlice.actions;\r\nexport default authSlice.reducer;"], "names": [], "mappings": ";;;;;;AAAA;;AAOA,kDAAkD;AAClD,MAAM,kBAAkB;IACpB,uCAAmC;;IAMnC;IACA,OAAO;QACH,YAAY;QACZ,MAAM;IACV;AACJ;AAEA,MAAM,eAA0B;AAEhC,MAAM,YAAY,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IAC1B,MAAM;IACN;IACA,UAAU;QACN,OAAO,CAAC,OAAO;YACX,MAAM,UAAU,GAAG;YACnB,MAAM,IAAI,GAAG,OAAO,OAAO,IAAI;QACnC;QACA,QAAQ,CAAC;YACL,MAAM,UAAU,GAAG;YACnB,MAAM,IAAI,GAAG;QACjB;QACA,SAAS,CAAC,OAAO;YACb,MAAM,IAAI,GAAG,OAAO,OAAO;QAC/B;IACJ;AACJ;AAEO,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,UAAU,OAAO;uCAC5C,UAAU,OAAO", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/redux/store.ts"], "sourcesContent": ["import { configureStore } from '@reduxjs/toolkit'\r\nimport authReducer from './slice/authSlice';\r\n\r\nexport const makeStore = () => {\r\n    return configureStore({\r\n        reducer: {\r\n            auth: authReducer,\r\n        }\r\n    })\r\n}\r\n\r\nexport type AppStore = ReturnType<typeof makeStore>\r\nexport type RootState = ReturnType<AppStore['getState']>\r\nexport type AppDispatch = AppStore['dispatch']"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,MAAM,YAAY;IACrB,OAAO,CAAA,GAAA,2LAAA,CAAA,iBAAc,AAAD,EAAE;QAClB,SAAS;YACL,MAAM,kIAAA,CAAA,UAAW;QACrB;IACJ;AACJ", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/redux/hook.ts"], "sourcesContent": ["import { useDispatch, useSelector, useStore } from 'react-redux'\r\nimport { AppDispatch, AppStore, RootState } from './store'\r\n\r\nexport const useAppDispatch = useDispatch.withTypes<AppDispatch>()\r\nexport const useAppSelector = useSelector.withTypes<RootState>()\r\nexport const useAppStore = useStore.withTypes<AppStore>()"], "names": [], "mappings": ";;;;;AAAA;;AAGO,MAAM,iBAAiB,yJAAA,CAAA,cAAW,CAAC,SAAS;AAC5C,MAAM,iBAAiB,yJAAA,CAAA,cAAW,CAAC,SAAS;AAC5C,MAAM,cAAc,yJAAA,CAAA,WAAQ,CAAC,SAAS", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/auth/AuthInitializer.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect } from 'react';\r\nimport { useAppDispatch } from '@/redux/hook';\r\nimport { login, logout } from '@/redux/slice/authSlice';\r\n\r\nconst AuthInitializer = ({ children }: { children: React.ReactNode }) => {\r\n    const dispatch = useAppDispatch();\r\n\r\n    useEffect(() => {\r\n        const token = localStorage.getItem('accessToken');\r\n        if (token) {\r\n            dispatch(login(null));\r\n        } else {\r\n            dispatch(logout());\r\n        }\r\n    }, [dispatch]);\r\n\r\n    return <>{children}</>;\r\n};\r\n\r\nexport default AuthInitializer;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,kBAAkB,CAAC,EAAE,QAAQ,EAAiC;IAChE,MAAM,WAAW,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD;IAE9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,OAAO;YACP,SAAS,CAAA,GAAA,kIAAA,CAAA,QAAK,AAAD,EAAE;QACnB,OAAO;YACH,SAAS,CAAA,GAAA,kIAAA,CAAA,SAAM,AAAD;QAClB;IACJ,GAAG;QAAC;KAAS;IAEb,qBAAO;kBAAG;;AACd;uCAEe", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/redux/StoreProvider.tsx"], "sourcesContent": ["'use client'\r\nimport { useRef } from 'react'\r\nimport { AppStore, makeStore } from './store'\r\nimport { Provider } from 'react-redux'\r\nimport AuthInitializer from '@/components/auth/AuthInitializer'\r\n\r\nexport default function StoreProvider({\r\n    children\r\n}: {\r\n    children: React.ReactNode\r\n}) {\r\n    const storeRef = useRef<AppStore | null>(null)\r\n    if (!storeRef.current) {\r\n        storeRef.current = makeStore()\r\n    }\r\n\r\n    return (\r\n        <Provider store={storeRef.current}>\r\n            <AuthInitializer>\r\n                {children}\r\n            </AuthInitializer>\r\n        </Provider>\r\n    )\r\n}"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAJA;;;;;;AAMe,SAAS,cAAc,EAClC,QAAQ,EAGX;IACG,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAmB;IACzC,IAAI,CAAC,SAAS,OAAO,EAAE;QACnB,SAAS,OAAO,GAAG,CAAA,GAAA,qHAAA,CAAA,YAAS,AAAD;IAC/B;IAEA,qBACI,8OAAC,yJAAA,CAAA,WAAQ;QAAC,OAAO,SAAS,OAAO;kBAC7B,cAAA,8OAAC,6IAAA,CAAA,UAAe;sBACX;;;;;;;;;;;AAIjB", "debugId": null}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/hooks/useAuth.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\n\r\nexport const useAuth = () => {\r\n    const [isLoggedIn, setIsLoggedIn] = useState(false);\r\n    const [loading, setLoading] = useState(true);\r\n\r\n    const checkAuthStatus = () => {\r\n        const token = localStorage.getItem('accessToken');\r\n        setIsLoggedIn(!!token);\r\n        setLoading(false);\r\n    };\r\n\r\n    useEffect(() => {\r\n        checkAuthStatus();\r\n\r\n        const handleStorageChange = (e: StorageEvent) => {\r\n            if (e.key === 'accessToken') {\r\n                checkAuthStatus();\r\n            }\r\n        };\r\n\r\n        const handleAuthChange = () => {\r\n            checkAuthStatus();\r\n        };\r\n\r\n        window.addEventListener('storage', handleStorageChange);\r\n        window.addEventListener('authChange', handleAuthChange);\r\n\r\n        return () => {\r\n            window.removeEventListener('storage', handleStorageChange);\r\n            window.removeEventListener('authChange', handleAuthChange);\r\n        };\r\n    }, []);\r\n\r\n    const login = (userData?: any) => {\r\n        setIsLoggedIn(true);\r\n        window.dispatchEvent(new CustomEvent('authChange'));\r\n    };\r\n\r\n    const logout = () => {\r\n        localStorage.removeItem('accessToken');\r\n        localStorage.removeItem('refreshToken');\r\n        setIsLoggedIn(false);\r\n        window.dispatchEvent(new CustomEvent('authChange'));\r\n    };\r\n\r\n    return {\r\n        isLoggedIn,\r\n        loading,\r\n        login,\r\n        logout,\r\n        checkAuthStatus\r\n    };\r\n};\r\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAIO,MAAM,UAAU;IACnB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,kBAAkB;QACpB,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,cAAc,CAAC,CAAC;QAChB,WAAW;IACf;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN;QAEA,MAAM,sBAAsB,CAAC;YACzB,IAAI,EAAE,GAAG,KAAK,eAAe;gBACzB;YACJ;QACJ;QAEA,MAAM,mBAAmB;YACrB;QACJ;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,gBAAgB,CAAC,cAAc;QAEtC,OAAO;YACH,OAAO,mBAAmB,CAAC,WAAW;YACtC,OAAO,mBAAmB,CAAC,cAAc;QAC7C;IACJ,GAAG,EAAE;IAEL,MAAM,QAAQ,CAAC;QACX,cAAc;QACd,OAAO,aAAa,CAAC,IAAI,YAAY;IACzC;IAEA,MAAM,SAAS;QACX,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,cAAc;QACd,OAAO,aAAa,CAAC,IAAI,YAAY;IACzC;IAEA,OAAO;QACH;QACA;QACA;QACA;QACA;IACJ;AACJ", "debugId": null}}, {"offset": {"line": 283, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/layouts/Header.tsx"], "sourcesContent": ["'use client'\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { Heart, Search, ShoppingBag, UserCircle, LogIn, UserPlus, History, LogOut, User, Calendar, Settings, Bell } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useAuth } from '@/hooks/useAuth';\r\n\r\nconst Header = () => {\r\n    const [isDropdownOpen, setIsDropdownOpen] = useState(false);\r\n    const router = useRouter();\r\n    const { isLoggedIn, logout: authLogout } = useAuth();\r\n\r\n    // Đóng dropdown khi click outside\r\n    useEffect(() => {\r\n        const handleClickOutside = (event: MouseEvent) => {\r\n            const target = event.target as Element;\r\n            if (!target.closest('.user-dropdown')) {\r\n                setIsDropdownOpen(false);\r\n            }\r\n        };\r\n\r\n        if (isDropdownOpen) {\r\n            document.addEventListener('mousedown', handleClickOutside);\r\n        }\r\n\r\n        return () => {\r\n            document.removeEventListener('mousedown', handleClickOutside);\r\n        };\r\n    }, [isDropdownOpen]);\r\n\r\n    const handleLogout = () => {\r\n        authLogout();\r\n        setIsDropdownOpen(false);\r\n        router.push(\"/login\");\r\n    };\r\n\r\n    return (\r\n        <div>\r\n            <header className=\"bg-white shadow-sm sticky top-0 z-50\">\r\n                <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n                    <div className=\"flex justify-between items-center h-16\">\r\n                        <div className=\"flex items-center space-x-2\">\r\n                            <div className=\"w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center\">\r\n                                <Heart className=\"w-6 h-6 text-white\" />\r\n                            </div>\r\n                            <span className=\"text-2xl font-bold text-gray-900\">Medically</span>\r\n                        </div>\r\n\r\n                        <nav className=\"hidden md:flex space-x-8\">\r\n                            <Link href=\"/\" className=\"text-blue-600 font-medium hover:text-blue-700 transition-colors\">Home</Link>\r\n                            <Link href=\"/about\" className=\"text-gray-600 hover:text-gray-900 transition-colors\">About</Link>\r\n                            <Link href=\"/doctor\" className=\"text-gray-600 hover:text-gray-900 transition-colors\">Doctors</Link>\r\n                            <Link href=\"/services\" className=\"text-gray-600 hover:text-gray-900 transition-colors\">Services</Link>\r\n                            <Link href=\"/portfolio\" className=\"text-gray-600 hover:text-gray-900 transition-colors\">Portfolio</Link>\r\n                            <Link href=\"/blog\" className=\"text-gray-600 hover:text-gray-900 transition-colors\">Blog</Link>\r\n                            <Link href=\"/contact\" className=\"text-gray-600 hover:text-gray-900 transition-colors\">Contact</Link>\r\n                        </nav>\r\n\r\n                        {/* Right side icons */}\r\n                        <div className=\"flex items-center space-x-6\">\r\n                            <Search className=\"w-6 h-6 text-gray-600 hover:text-gray-900 cursor-pointer transition-colors\" />\r\n                            <div className=\"relative\">\r\n                                <ShoppingBag className=\"w-6 h-6 text-gray-600 hover:text-gray-900 cursor-pointer transition-colors\" />\r\n                                <span className=\"absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\">0</span>\r\n                            </div>\r\n\r\n                            {/* User Dropdown */}\r\n                            <div className=\"relative user-dropdown\">\r\n                                <button\r\n                                    onClick={() => setIsDropdownOpen(!isDropdownOpen)}\r\n                                    className=\"flex items-center space-x-2 focus:outline-none bg-blue-50 hover:bg-blue-100 p-2 rounded-full transition-colors\"\r\n                                >\r\n                                    <UserCircle className=\"w-7 h-7 text-blue-600\" />\r\n                                </button>\r\n\r\n                                {/* Dropdown Menu */}\r\n                                {isDropdownOpen && (\r\n                                    <div className=\"absolute right-0 mt-3 w-64 bg-white rounded-lg shadow-xl py-2 z-50 border border-gray-200\">\r\n                                        <div className=\"px-4 py-3 border-b border-gray-200\">\r\n                                            {isLoggedIn ? (\r\n                                                <>\r\n                                                    <p className=\"text-sm text-gray-500\">Xin chào</p>\r\n                                                    <p className=\"font-medium text-gray-900\">Bệnh nhân</p>\r\n                                                </>\r\n                                            ) : (\r\n                                                <>\r\n                                                    <p className=\"text-sm text-gray-500\">Welcome to</p>\r\n                                                    <p className=\"font-medium text-gray-900\">Medically Healthcare</p>\r\n                                                </>\r\n                                            )}\r\n                                        </div>\r\n\r\n                                        {!isLoggedIn &&\r\n                                            <>\r\n                                                <Link href=\"/login\" className=\"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 transition-colors\">\r\n                                                    <LogIn className=\"w-5 h-5 mr-3 text-blue-600\" />\r\n                                                    <span className=\"text-base\">Login</span>\r\n                                                </Link>\r\n\r\n                                                <Link href=\"/registration\" className=\"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 transition-colors\">\r\n                                                    <UserPlus className=\"w-5 h-5 mr-3 text-blue-600\" />\r\n                                                    <span className=\"text-base\">Register</span>\r\n                                                </Link>\r\n                                            </>\r\n                                        }\r\n\r\n\r\n                                        {isLoggedIn && (\r\n                                            <>\r\n                                                <Link href=\"/profile\" className=\"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 transition-colors\">\r\n                                                    <User className=\"w-5 h-5 mr-3 text-blue-600\" />\r\n                                                    <span className=\"text-base\">Hồ sơ cá nhân</span>\r\n                                                </Link>\r\n\r\n                                                <Link href=\"/appointments\" className=\"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 transition-colors\">\r\n                                                    <Calendar className=\"w-5 h-5 mr-3 text-blue-600\" />\r\n                                                    <span className=\"text-base\">Lịch hẹn</span>\r\n                                                </Link>\r\n\r\n                                                <Link href=\"/notifications\" className=\"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 transition-colors\">\r\n                                                    <Bell className=\"w-5 h-5 mr-3 text-blue-600\" />\r\n                                                    <span className=\"text-base\">Thông báo</span>\r\n                                                </Link>\r\n\r\n                                                <Link href=\"/settings\" className=\"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 transition-colors\">\r\n                                                    <Settings className=\"w-5 h-5 mr-3 text-blue-600\" />\r\n                                                    <span className=\"text-base\">Cài đặt</span>\r\n                                                </Link>\r\n\r\n                                                <Link href=\"/appointments\" className=\"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 transition-colors\">\r\n                                                    <History className=\"w-5 h-5 mr-3 text-blue-600\" />\r\n                                                    <span className=\"text-base\">Lịch sử đặt hẹn</span>\r\n                                                </Link>\r\n\r\n                                                <div className=\"border-t border-gray-200 my-2\"></div>\r\n                                            </>\r\n                                        )}\r\n\r\n\r\n                                        {\r\n                                            isLoggedIn &&\r\n                                            <button\r\n                                                onClick={handleLogout}\r\n                                                className=\"flex items-center px-4 py-3 text-red-600 hover:bg-red-50 transition-colors w-full text-left\"\r\n                                            >\r\n                                                <LogOut className=\"w-5 h-5 mr-3\" />\r\n                                                <span className=\"text-base font-medium\">Logout</span>\r\n                                            </button>\r\n                                        }\r\n                                    </div>\r\n                                )}\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </header>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Header;"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AALA;;;;;;;AAOA,MAAM,SAAS;IACX,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,UAAU,EAAE,QAAQ,UAAU,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IAEjD,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,qBAAqB,CAAC;YACxB,MAAM,SAAS,MAAM,MAAM;YAC3B,IAAI,CAAC,OAAO,OAAO,CAAC,mBAAmB;gBACnC,kBAAkB;YACtB;QACJ;QAEA,IAAI,gBAAgB;YAChB,SAAS,gBAAgB,CAAC,aAAa;QAC3C;QAEA,OAAO;YACH,SAAS,mBAAmB,CAAC,aAAa;QAC9C;IACJ,GAAG;QAAC;KAAe;IAEnB,MAAM,eAAe;QACjB;QACA,kBAAkB;QAClB,OAAO,IAAI,CAAC;IAChB;IAEA,qBACI,8OAAC;kBACG,cAAA,8OAAC;YAAO,WAAU;sBACd,cAAA,8OAAC;gBAAI,WAAU;0BACX,cAAA,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCAAI,WAAU;8CACX,cAAA,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAErB,8OAAC;oCAAK,WAAU;8CAAmC;;;;;;;;;;;;sCAGvD,8OAAC;4BAAI,WAAU;;8CACX,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAkE;;;;;;8CAC3F,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAsD;;;;;;8CACpF,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAU,WAAU;8CAAsD;;;;;;8CACrF,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAU;8CAAsD;;;;;;8CACvF,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;8CAAsD;;;;;;8CACxF,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;8CAAsD;;;;;;8CACnF,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAsD;;;;;;;;;;;;sCAI1F,8OAAC;4BAAI,WAAU;;8CACX,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCAAI,WAAU;;sDACX,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,8OAAC;4CAAK,WAAU;sDAA+G;;;;;;;;;;;;8CAInI,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;4CACG,SAAS,IAAM,kBAAkB,CAAC;4CAClC,WAAU;sDAEV,cAAA,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;wCAIzB,gCACG,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;oDAAI,WAAU;8DACV,2BACG;;0EACI,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,8OAAC;gEAAE,WAAU;0EAA4B;;;;;;;qFAG7C;;0EACI,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,8OAAC;gEAAE,WAAU;0EAA4B;;;;;;;;;;;;;gDAKpD,CAAC,4BACE;;sEACI,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAS,WAAU;;8EAC1B,8OAAC,wMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,8OAAC;oEAAK,WAAU;8EAAY;;;;;;;;;;;;sEAGhC,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAgB,WAAU;;8EACjC,8OAAC,8MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,8OAAC;oEAAK,WAAU;8EAAY;;;;;;;;;;;;;;gDAMvC,4BACG;;sEACI,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAW,WAAU;;8EAC5B,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,8OAAC;oEAAK,WAAU;8EAAY;;;;;;;;;;;;sEAGhC,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAgB,WAAU;;8EACjC,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,8OAAC;oEAAK,WAAU;8EAAY;;;;;;;;;;;;sEAGhC,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAiB,WAAU;;8EAClC,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,8OAAC;oEAAK,WAAU;8EAAY;;;;;;;;;;;;sEAGhC,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAY,WAAU;;8EAC7B,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,8OAAC;oEAAK,WAAU;8EAAY;;;;;;;;;;;;sEAGhC,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAgB,WAAU;;8EACjC,8OAAC,wMAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;8EACnB,8OAAC;oEAAK,WAAU;8EAAY;;;;;;;;;;;;sEAGhC,8OAAC;4DAAI,WAAU;;;;;;;;gDAMnB,4BACA,8OAAC;oDACG,SAAS;oDACT,WAAU;;sEAEV,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYxF;uCAEe", "debugId": null}}, {"offset": {"line": 810, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/layouts/Footer.tsx"], "sourcesContent": ["import { Heart } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\n\r\nconst Footer = () => {\r\n    return (\r\n        <footer className=\"bg-gray-900 text-white py-12\">\r\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n                <div className=\"grid md:grid-cols-4 gap-8\">\r\n                    <div>\r\n                        <div className=\"flex items-center space-x-2 mb-4\">\r\n                            <div className=\"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center\">\r\n                                <Heart className=\"w-5 h-5 text-white\" />\r\n                            </div>\r\n                            <span className=\"text-xl font-bold\">Medically</span>\r\n                        </div>\r\n                        <p className=\"text-gray-400\">\r\n                            Providing quality healthcare services to help you live a healthy and happy life.\r\n                        </p>\r\n                    </div>\r\n\r\n                    <div>\r\n                        <h3 className=\"font-semibold mb-4\">Quick Links</h3>\r\n                        <ul className=\"space-y-2 text-gray-400\">\r\n                            <li><Link href=\"#\" className=\"hover:text-white transition-colors\">About Us</Link></li>\r\n                            <li><Link href=\"#\" className=\"hover:text-white transition-colors\">Services</Link></li>\r\n                            <li><Link href=\"#\" className=\"hover:text-white transition-colors\">Doctors</Link></li>\r\n                            <li><Link href=\"#\" className=\"hover:text-white transition-colors\">Contact</Link></li>\r\n                        </ul>\r\n                    </div>\r\n\r\n                    <div>\r\n                        <h3 className=\"font-semibold mb-4\">Services</h3>\r\n                        <ul className=\"space-y-2 text-gray-400\">\r\n                            <li><Link href=\"#\" className=\"hover:text-white transition-colors\">Cardiology</Link></li>\r\n                            <li><Link href=\"#\" className=\"hover:text-white transition-colors\">Family Medicine</Link></li>\r\n                            <li><Link href=\"#\" className=\"hover:text-white transition-colors\">Emergency Care</Link></li>\r\n                            <li><Link href=\"#\" className=\"hover:text-white transition-colors\">Pediatrics</Link></li>\r\n                        </ul>\r\n                    </div>\r\n\r\n                    <div>\r\n                        <h3 className=\"font-semibold mb-4\">Contact Info</h3>\r\n                        <div className=\"space-y-2 text-gray-400\">\r\n                            <p>123 Medical Center Drive</p>\r\n                            <p>Healthcare City, HC 12345</p>\r\n                            <p>Phone: (*************</p>\r\n                            <p>Email: <EMAIL></p>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div className=\"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400\">\r\n                    <p>&copy; 2025 Medically. All rights reserved.</p>\r\n                </div>\r\n            </div>\r\n        </footer>\r\n    )\r\n}\r\n\r\nexport default Footer;"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,SAAS;IACX,qBACI,8OAAC;QAAO,WAAU;kBACd,cAAA,8OAAC;YAAI,WAAU;;8BACX,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;;8CACG,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;4CAAI,WAAU;sDACX,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAErB,8OAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;8CAExC,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAKjC,8OAAC;;8CACG,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,8OAAC;oCAAG,WAAU;;sDACV,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAqC;;;;;;;;;;;sDAClE,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAqC;;;;;;;;;;;sDAClE,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAqC;;;;;;;;;;;sDAClE,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;sCAI1E,8OAAC;;8CACG,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,8OAAC;oCAAG,WAAU;;sDACV,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAqC;;;;;;;;;;;sDAClE,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAqC;;;;;;;;;;;sDAClE,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAqC;;;;;;;;;;;sDAClE,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;sCAI1E,8OAAC;;8CACG,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;8BAKf,8OAAC;oBAAI,WAAU;8BACX,cAAA,8OAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAKvB;uCAEe", "debugId": null}}, {"offset": {"line": 1137, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/layouts/LayoutWrapper.tsx"], "sourcesContent": ["'use client'\r\nimport { usePathname } from \"next/navigation\";\r\nimport Header from \"@/components/layouts/Header\";\r\nimport Footer from \"@/components/layouts/Footer\";\r\n\r\nexport default function LayoutWrapper({\r\n    children\r\n}: {\r\n    children: React.ReactNode\r\n}) {\r\n    const pathname = usePathname();\r\n\r\n    const noLayoutPages = ['/login', '/register', '/registration', \"/receptionist\", \"/forgot-password\"];\r\n    const shouldShowLayout = !noLayoutPages.includes(pathname);\r\n\r\n    return (\r\n        <>\r\n            {shouldShowLayout && <Header />}\r\n            {children}\r\n            {shouldShowLayout && <Footer />}\r\n        </>\r\n    );\r\n}"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;AAKe,SAAS,cAAc,EAClC,QAAQ,EAGX;IACG,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,gBAAgB;QAAC;QAAU;QAAa;QAAiB;QAAiB;KAAmB;IACnG,MAAM,mBAAmB,CAAC,cAAc,QAAQ,CAAC;IAEjD,qBACI;;YACK,kCAAoB,8OAAC,uIAAA,CAAA,UAAM;;;;;YAC3B;YACA,kCAAoB,8OAAC,uIAAA,CAAA,UAAM;;;;;;;AAGxC", "debugId": null}}]}