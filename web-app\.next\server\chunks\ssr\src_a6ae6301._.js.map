{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/utils/baseUrl.ts"], "sourcesContent": ["export const API_URL = process.env.NEXT_PUBLIC_API_URL || 'https://localhost:7166';\r\n"], "names": [], "mappings": ";;;AAAO,MAAM,UAAU,8DAAmC", "debugId": null}}, {"offset": {"line": 17, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/services/appointmentService.ts"], "sourcesContent": ["import { API_URL } from \"@/utils/baseUrl\";\r\nimport { ApiResponse } from '@/types/apiResonse';\r\nimport {\r\n    AppointmentResponse,\r\n    CancelAppointmentRequest,\r\n    RescheduleAppointmentRequest,\r\n    RescheduleAppointmentResponse\r\n} from '@/types/appointment';\r\n\r\nclass AppointmentService {\r\n    private baseUrl = `${API_URL}/api/v1/appointments`;\r\n\r\n    // L<PERSON>y danh sách cuộc hẹn của bệnh nhân\r\n    async getMyAppointments(): Promise<ApiResponse<AppointmentResponse[]>> {\r\n        try {\r\n            const token = localStorage.getItem('accessToken');\r\n\r\n            const response = await fetch(this.baseUrl, {\r\n                method: 'GET',\r\n                headers: {\r\n                    'Content-Type': 'application/json',\r\n                    'Authorization': `Bearer ${token}`,\r\n                },\r\n            });\r\n\r\n            if (!response.ok) {\r\n                throw new Error(`HTTP error! status: ${response.status}`);\r\n            }\r\n\r\n            const data: ApiResponse<AppointmentResponse[]> = await response.json();\r\n            return data;\r\n        } catch (error) {\r\n            console.error('Error fetching appointments:', error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    // Hủy cuộc hẹn\r\n    async cancelAppointment(appointmentId: number, request: CancelAppointmentRequest): Promise<ApiResponse<object>> {\r\n        try {\r\n            const token = localStorage.getItem('accessToken');\r\n\r\n            const response = await fetch(`${this.baseUrl}/${appointmentId}/cancel`, {\r\n                method: 'PUT',\r\n                headers: {\r\n                    'Content-Type': 'application/json',\r\n                    'Authorization': `Bearer ${token}`,\r\n                },\r\n                body: JSON.stringify(request),\r\n            });\r\n\r\n            if (!response.ok) {\r\n                throw new Error(`HTTP error! status: ${response.status}`);\r\n            }\r\n\r\n            const data: ApiResponse<object> = await response.json();\r\n            return data;\r\n        } catch (error) {\r\n            console.error('Error canceling appointment:', error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    // Đổi lịch cuộc hẹn\r\n    async rescheduleAppointment(\r\n        appointmentId: number,\r\n        request: RescheduleAppointmentRequest\r\n    ): Promise<ApiResponse<RescheduleAppointmentResponse>> {\r\n        try {\r\n            const token = localStorage.getItem('accessToken');\r\n\r\n            const response = await fetch(`${this.baseUrl}/${appointmentId}/reschedule`, {\r\n                method: 'PUT',\r\n                headers: {\r\n                    'Content-Type': 'application/json',\r\n                    'Authorization': `Bearer ${token}`,\r\n                },\r\n                body: JSON.stringify(request),\r\n            });\r\n\r\n            if (!response.ok) {\r\n                throw new Error(`HTTP error! status: ${response.status}`);\r\n            }\r\n\r\n            const data: ApiResponse<RescheduleAppointmentResponse> = await response.json();\r\n            return data;\r\n        } catch (error) {\r\n            console.error('Error rescheduling appointment:', error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    async createAppointment(payload: {\r\n        doctorId: number;\r\n        slotId: number;\r\n        appointmentDate: string;\r\n        reasonForVisit: string;\r\n        packageId: number;\r\n    }) {\r\n        const token = localStorage.getItem('accessToken');\r\n        const response = await fetch(`${API_URL}/api/v1/appointments/create`, {\r\n            method: 'POST',\r\n            headers: {\r\n                'Content-Type': 'application/json',\r\n                'Authorization': `Bearer ${token}`,\r\n            },\r\n            body: JSON.stringify(payload),\r\n        });\r\n        if (!response.ok) {\r\n            throw new Error(`HTTP error! status: ${response.status}`);\r\n        }\r\n        return response.json();\r\n    }\r\n}\r\n\r\nexport const appointmentService = new AppointmentService(); "], "names": [], "mappings": ";;;AAAA;;AASA,MAAM;IACM,UAAU,GAAG,uHAAA,CAAA,UAAO,CAAC,oBAAoB,CAAC,CAAC;IAEnD,uCAAuC;IACvC,MAAM,oBAAiE;QACnE,IAAI;YACA,MAAM,QAAQ,aAAa,OAAO,CAAC;YAEnC,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE;gBACvC,QAAQ;gBACR,SAAS;oBACL,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACtC;YACJ;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBACd,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC5D;YAEA,MAAM,OAA2C,MAAM,SAAS,IAAI;YACpE,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACV;IACJ;IAEA,eAAe;IACf,MAAM,kBAAkB,aAAqB,EAAE,OAAiC,EAAgC;QAC5G,IAAI;YACA,MAAM,QAAQ,aAAa,OAAO,CAAC;YAEnC,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,cAAc,OAAO,CAAC,EAAE;gBACpE,QAAQ;gBACR,SAAS;oBACL,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACtC;gBACA,MAAM,KAAK,SAAS,CAAC;YACzB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBACd,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC5D;YAEA,MAAM,OAA4B,MAAM,SAAS,IAAI;YACrD,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACV;IACJ;IAEA,oBAAoB;IACpB,MAAM,sBACF,aAAqB,EACrB,OAAqC,EACc;QACnD,IAAI;YACA,MAAM,QAAQ,aAAa,OAAO,CAAC;YAEnC,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,cAAc,WAAW,CAAC,EAAE;gBACxE,QAAQ;gBACR,SAAS;oBACL,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACtC;gBACA,MAAM,KAAK,SAAS,CAAC;YACzB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBACd,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC5D;YAEA,MAAM,OAAmD,MAAM,SAAS,IAAI;YAC5E,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACV;IACJ;IAEA,MAAM,kBAAkB,OAMvB,EAAE;QACC,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,MAAM,WAAW,MAAM,MAAM,GAAG,uHAAA,CAAA,UAAO,CAAC,2BAA2B,CAAC,EAAE;YAClE,QAAQ;YACR,SAAS;gBACL,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;YACtC;YACA,MAAM,KAAK,SAAS,CAAC;QACzB;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YACd,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC5D;QACA,OAAO,SAAS,IAAI;IACxB;AACJ;AAEO,MAAM,qBAAqB,IAAI", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/hooks/useAppointments.ts"], "sourcesContent": ["import { useState, useEffect } from 'react';\r\nimport { appointmentService } from '@/services/appointmentService';\r\nimport { AppointmentResponse, CancelAppointmentRequest, RescheduleAppointmentRequest } from '@/types/appointment';\r\nimport { ApiResponse } from '@/types/apiResonse';\r\n\r\nexport const useAppointments = () => {\r\n    const [appointments, setAppointments] = useState<AppointmentResponse[]>([]);\r\n    const [loading, setLoading] = useState(false);\r\n    const [error, setError] = useState<string | null>(null);\r\n\r\n    // Lấy danh sách cuộc hẹn\r\n    const fetchAppointments = async () => {\r\n        setLoading(true);\r\n        setError(null);\r\n        try {\r\n            const response: ApiResponse<AppointmentResponse[]> = await appointmentService.getMyAppointments();\r\n            if (response.code === 200 && response.result) {\r\n                setAppointments(response.result);\r\n            } else {\r\n                setError(response.message || 'Failed to fetch appointments');\r\n            }\r\n        } catch (err) {\r\n            setError(err instanceof Error ? err.message : 'An error occurred');\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    // Hủy cuộc hẹn\r\n    const cancelAppointment = async (appointmentId: number, cancelReason: string) => {\r\n        setLoading(true);\r\n        setError(null);\r\n        try {\r\n            const request: CancelAppointmentRequest = { cancelReason };\r\n            const response = await appointmentService.cancelAppointment(appointmentId, request);\r\n            \r\n            if (response.code === 200) {\r\n                // Refresh danh sách sau khi hủy thành công\r\n                await fetchAppointments();\r\n                return { success: true, message: response.message };\r\n            } else {\r\n                setError(response.message || 'Failed to cancel appointment');\r\n                return { success: false, message: response.message };\r\n            }\r\n        } catch (err) {\r\n            const errorMessage = err instanceof Error ? err.message : 'An error occurred';\r\n            setError(errorMessage);\r\n            return { success: false, message: errorMessage };\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    // Đổi lịch cuộc hẹn\r\n    const rescheduleAppointment = async (\r\n        appointmentId: number, \r\n        newSlotId: number, \r\n        newAppointmentDate: string, \r\n        reason: string\r\n    ) => {\r\n        setLoading(true);\r\n        setError(null);\r\n        try {\r\n            const request: RescheduleAppointmentRequest = {\r\n                newSlotId,\r\n                newAppointmentDate,\r\n                reason\r\n            };\r\n            const response = await appointmentService.rescheduleAppointment(appointmentId, request);\r\n            \r\n            if (response.code === 200) {\r\n                // Refresh danh sách sau khi đổi lịch thành công\r\n                await fetchAppointments();\r\n                return { success: true, message: response.message };\r\n            } else {\r\n                setError(response.message || 'Failed to reschedule appointment');\r\n                return { success: false, message: response.message };\r\n            }\r\n        } catch (err) {\r\n            const errorMessage = err instanceof Error ? err.message : 'An error occurred';\r\n            setError(errorMessage);\r\n            return { success: false, message: errorMessage };\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    // Lọc cuộc hẹn theo trạng thái\r\n    const getAppointmentsByStatus = (status: string) => {\r\n        return appointments.filter(appointment => appointment.status === status);\r\n    };\r\n\r\n    // Lấy cuộc hẹn sắp tới\r\n    const getUpcomingAppointments = () => {\r\n        const now = new Date();\r\n        return appointments.filter(appointment => {\r\n            const appointmentDateTime = new Date(`${appointment.appointmentDate} ${appointment.appointmentTime}`);\r\n            return appointmentDateTime > now && appointment.status !== 'cancelled';\r\n        });\r\n    };\r\n\r\n    // Lấy cuộc hẹn đã hoàn thành\r\n    const getCompletedAppointments = () => {\r\n        return appointments.filter(appointment => appointment.status === 'completed');\r\n    };\r\n\r\n    // Lấy cuộc hẹn đã hủy\r\n    const getCancelledAppointments = () => {\r\n        return appointments.filter(appointment => appointment.status === 'cancelled');\r\n    };\r\n\r\n    useEffect(() => {\r\n        fetchAppointments();\r\n    }, []);\r\n\r\n    return {\r\n        appointments,\r\n        loading,\r\n        error,\r\n        fetchAppointments,\r\n        cancelAppointment,\r\n        rescheduleAppointment,\r\n        getAppointmentsByStatus,\r\n        getUpcomingAppointments,\r\n        getCompletedAppointments,\r\n        getCancelledAppointments,\r\n    };\r\n};"], "names": [], "mappings": ";;;AAAA;AACA;;;AAIO,MAAM,kBAAkB;IAC3B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB,EAAE;IAC1E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,yBAAyB;IACzB,MAAM,oBAAoB;QACtB,WAAW;QACX,SAAS;QACT,IAAI;YACA,MAAM,WAA+C,MAAM,qIAAA,CAAA,qBAAkB,CAAC,iBAAiB;YAC/F,IAAI,SAAS,IAAI,KAAK,OAAO,SAAS,MAAM,EAAE;gBAC1C,gBAAgB,SAAS,MAAM;YACnC,OAAO;gBACH,SAAS,SAAS,OAAO,IAAI;YACjC;QACJ,EAAE,OAAO,KAAK;YACV,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAClD,SAAU;YACN,WAAW;QACf;IACJ;IAEA,eAAe;IACf,MAAM,oBAAoB,OAAO,eAAuB;QACpD,WAAW;QACX,SAAS;QACT,IAAI;YACA,MAAM,UAAoC;gBAAE;YAAa;YACzD,MAAM,WAAW,MAAM,qIAAA,CAAA,qBAAkB,CAAC,iBAAiB,CAAC,eAAe;YAE3E,IAAI,SAAS,IAAI,KAAK,KAAK;gBACvB,2CAA2C;gBAC3C,MAAM;gBACN,OAAO;oBAAE,SAAS;oBAAM,SAAS,SAAS,OAAO;gBAAC;YACtD,OAAO;gBACH,SAAS,SAAS,OAAO,IAAI;gBAC7B,OAAO;oBAAE,SAAS;oBAAO,SAAS,SAAS,OAAO;gBAAC;YACvD;QACJ,EAAE,OAAO,KAAK;YACV,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,OAAO;gBAAE,SAAS;gBAAO,SAAS;YAAa;QACnD,SAAU;YACN,WAAW;QACf;IACJ;IAEA,oBAAoB;IACpB,MAAM,wBAAwB,OAC1B,eACA,WACA,oBACA;QAEA,WAAW;QACX,SAAS;QACT,IAAI;YACA,MAAM,UAAwC;gBAC1C;gBACA;gBACA;YACJ;YACA,MAAM,WAAW,MAAM,qIAAA,CAAA,qBAAkB,CAAC,qBAAqB,CAAC,eAAe;YAE/E,IAAI,SAAS,IAAI,KAAK,KAAK;gBACvB,gDAAgD;gBAChD,MAAM;gBACN,OAAO;oBAAE,SAAS;oBAAM,SAAS,SAAS,OAAO;gBAAC;YACtD,OAAO;gBACH,SAAS,SAAS,OAAO,IAAI;gBAC7B,OAAO;oBAAE,SAAS;oBAAO,SAAS,SAAS,OAAO;gBAAC;YACvD;QACJ,EAAE,OAAO,KAAK;YACV,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,OAAO;gBAAE,SAAS;gBAAO,SAAS;YAAa;QACnD,SAAU;YACN,WAAW;QACf;IACJ;IAEA,+BAA+B;IAC/B,MAAM,0BAA0B,CAAC;QAC7B,OAAO,aAAa,MAAM,CAAC,CAAA,cAAe,YAAY,MAAM,KAAK;IACrE;IAEA,uBAAuB;IACvB,MAAM,0BAA0B;QAC5B,MAAM,MAAM,IAAI;QAChB,OAAO,aAAa,MAAM,CAAC,CAAA;YACvB,MAAM,sBAAsB,IAAI,KAAK,GAAG,YAAY,eAAe,CAAC,CAAC,EAAE,YAAY,eAAe,EAAE;YACpG,OAAO,sBAAsB,OAAO,YAAY,MAAM,KAAK;QAC/D;IACJ;IAEA,6BAA6B;IAC7B,MAAM,2BAA2B;QAC7B,OAAO,aAAa,MAAM,CAAC,CAAA,cAAe,YAAY,MAAM,KAAK;IACrE;IAEA,sBAAsB;IACtB,MAAM,2BAA2B;QAC7B,OAAO,aAAa,MAAM,CAAC,CAAA,cAAe,YAAY,MAAM,KAAK;IACrE;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN;IACJ,GAAG,EAAE;IAEL,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACJ;AACJ", "debugId": null}}, {"offset": {"line": 252, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/hooks/useToast.ts"], "sourcesContent": ["import { useState, useCallback } from 'react';\r\n\r\ninterface ToastState {\r\n    show: boolean;\r\n    message: string;\r\n    type: 'success' | 'error' | 'info';\r\n}\r\n\r\nexport const useToast = () => {\r\n    const [toast, setToast] = useState<ToastState>({\r\n        show: false,\r\n        message: '',\r\n        type: 'info'\r\n    });\r\n\r\n    const showToast = useCallback((message: string, type: 'success' | 'error' | 'info' = 'info') => {\r\n        setToast({\r\n            show: true,\r\n            message,\r\n            type\r\n        });\r\n    }, []);\r\n\r\n    const hideToast = useCallback(() => {\r\n        setToast(prev => ({\r\n            ...prev,\r\n            show: false\r\n        }));\r\n    }, []);\r\n\r\n    const showSuccess = useCallback((message: string) => {\r\n        showToast(message, 'success');\r\n    }, [showToast]);\r\n\r\n    const showError = useCallback((message: string) => {\r\n        showToast(message, 'error');\r\n    }, [showToast]);\r\n\r\n    const showInfo = useCallback((message: string) => {\r\n        showToast(message, 'info');\r\n    }, [showToast]);\r\n\r\n    return {\r\n        toast,\r\n        showToast,\r\n        hideToast,\r\n        showSuccess,\r\n        showError,\r\n        showInfo\r\n    };\r\n}; "], "names": [], "mappings": ";;;AAAA;;AAQO,MAAM,WAAW;IACpB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;QAC3C,MAAM;QACN,SAAS;QACT,MAAM;IACV;IAEA,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,SAAiB,OAAqC,MAAM;QACvF,SAAS;YACL,MAAM;YACN;YACA;QACJ;IACJ,GAAG,EAAE;IAEL,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC1B,SAAS,CAAA,OAAQ,CAAC;gBACd,GAAG,IAAI;gBACP,MAAM;YACV,CAAC;IACL,GAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC7B,UAAU,SAAS;IACvB,GAAG;QAAC;KAAU;IAEd,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC3B,UAAU,SAAS;IACvB,GAAG;QAAC;KAAU;IAEd,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC1B,UAAU,SAAS;IACvB,GAAG;QAAC;KAAU;IAEd,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;IACJ;AACJ", "debugId": null}}, {"offset": {"line": 306, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/utils/dateUtils.ts"], "sourcesContent": ["import { DateInfo } from '../types/doctor';\r\n\r\nexport const formatDate = (date: Date): DateInfo => {\r\n    return {\r\n        day: date.toLocaleDateString('en-US', { weekday: 'short' }),\r\n        date: date.getDate(),\r\n        month: date.toLocaleDateString('en-US', { month: 'short' })\r\n    };\r\n};\r\n\r\nexport const formatDateString = (dateString: string): string => {\r\n    const date = new Date(dateString);\r\n    return date.toLocaleDateString('vi-VN', {\r\n        weekday: 'long',\r\n        year: 'numeric',\r\n        month: 'long',\r\n        day: 'numeric'\r\n    });\r\n};\r\n\r\nexport const getNextDays = (count: number = 14): Date[] => {\r\n    const today = new Date();\r\n    return Array.from({ length: count }, (_, i) => {\r\n        const date = new Date(today);\r\n        date.setDate(today.getDate() + i + 1);\r\n        return date;\r\n    });\r\n};\r\n\r\nexport const formatDateForInput = (date: Date): string => {\r\n    return date.toISOString().split('T')[0];\r\n};"], "names": [], "mappings": ";;;;;;AAEO,MAAM,aAAa,CAAC;IACvB,OAAO;QACH,KAAK,KAAK,kBAAkB,CAAC,SAAS;YAAE,SAAS;QAAQ;QACzD,MAAM,KAAK,OAAO;QAClB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YAAE,OAAO;QAAQ;IAC7D;AACJ;AAEO,MAAM,mBAAmB,CAAC;IAC7B,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACpC,SAAS;QACT,MAAM;QACN,OAAO;QACP,KAAK;IACT;AACJ;AAEO,MAAM,cAAc,CAAC,QAAgB,EAAE;IAC1C,MAAM,QAAQ,IAAI;IAClB,OAAO,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAM,GAAG,CAAC,GAAG;QACrC,MAAM,OAAO,IAAI,KAAK;QACtB,KAAK,OAAO,CAAC,MAAM,OAAO,KAAK,IAAI;QACnC,OAAO;IACX;AACJ;AAEO,MAAM,qBAAqB,CAAC;IAC/B,OAAO,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;AAC3C", "debugId": null}}, {"offset": {"line": 351, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/patient/AppointmentHistory/CancelAppointmentModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport { AppointmentResponse } from '@/types/appointment';\r\nimport { useAppointments } from '@/hooks/useAppointments';\r\nimport { useToast } from '@/hooks/useToast';\r\n\r\ninterface CancelAppointmentModalProps {\r\n    appointment: AppointmentResponse;\r\n    onClose: () => void;\r\n    onSuccess: () => void;\r\n}\r\n\r\nconst CancelAppointmentModal: React.FC<CancelAppointmentModalProps> = ({\r\n    appointment,\r\n    onClose,\r\n    onSuccess\r\n}) => {\r\n    const [cancelReason, setCancelReason] = useState('');\r\n    const [loading, setLoading] = useState(false);\r\n    const { cancelAppointment } = useAppointments();\r\n    const { showSuccess, showError } = useToast();\r\n\r\n    const handleSubmit = async (e: React.FormEvent) => {\r\n        e.preventDefault();\r\n        \r\n        if (!cancelReason.trim()) {\r\n            showError('<PERSON><PERSON> lòng nhập lý do hủy lịch hẹn');\r\n            return;\r\n        }\r\n\r\n        setLoading(true);\r\n\r\n        try {\r\n            const result = await cancelAppointment(appointment.appointmentId, cancelReason);\r\n            \r\n            if (result.success) {\r\n                showSuccess('Hủy lịch hẹn thành công');\r\n                onSuccess();\r\n            } else {\r\n                showError(result.message || 'Có lỗi xảy ra khi hủy lịch hẹn');\r\n            }\r\n        } catch (err) {\r\n            showError('Có lỗi xảy ra khi hủy lịch hẹn');\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n            <div className=\"bg-white rounded-lg max-w-md w-full p-6\">\r\n                <div className=\"flex items-center justify-between mb-4\">\r\n                    <h3 className=\"text-lg font-semibold text-gray-900\">Hủy lịch hẹn</h3>\r\n                    <button\r\n                        onClick={onClose}\r\n                        className=\"text-gray-400 hover:text-gray-600\"\r\n                        disabled={loading}\r\n                    >\r\n                        <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                        </svg>\r\n                    </button>\r\n                </div>\r\n\r\n                <div className=\"mb-4\">\r\n                    <p className=\"text-gray-600 mb-2\">\r\n                        Bạn có chắc chắn muốn hủy lịch hẹn này?\r\n                    </p>\r\n                    <div className=\"bg-gray-50 p-3 rounded-md\">\r\n                        <p className=\"text-sm font-medium text-gray-900\">\r\n                            #{appointment.appointmentNumber} - {appointment.doctor.fullName}\r\n                        </p>\r\n                        <p className=\"text-sm text-gray-600\">\r\n                            {appointment.appointmentDate} - {appointment.appointmentTime}\r\n                        </p>\r\n                    </div>\r\n                </div>\r\n\r\n                <form onSubmit={handleSubmit} className=\"space-y-4\">\r\n                    <div>\r\n                        <label htmlFor=\"cancelReason\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                            Lý do hủy <span className=\"text-red-500\">*</span>\r\n                        </label>\r\n                        <textarea\r\n                            id=\"cancelReason\"\r\n                            value={cancelReason}\r\n                            onChange={(e) => setCancelReason(e.target.value)}\r\n                            rows={3}\r\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500\"\r\n                            placeholder=\"Nhập lý do hủy lịch hẹn...\"\r\n                            disabled={loading}\r\n                        />\r\n                    </div>\r\n\r\n                    <div className=\"flex gap-3\">\r\n                        <button\r\n                            type=\"button\"\r\n                            onClick={onClose}\r\n                            disabled={loading}\r\n                            className=\"flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\r\n                        >\r\n                            Hủy\r\n                        </button>\r\n                        <button\r\n                            type=\"submit\"\r\n                            disabled={loading}\r\n                            className=\"flex-1 px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\r\n                        >\r\n                            {loading ? (\r\n                                <div className=\"flex items-center justify-center gap-2\">\r\n                                    <svg className=\"animate-spin h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                                        <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" />\r\n                                        <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\" />\r\n                                    </svg>\r\n                                    Đang xử lý...\r\n                                </div>\r\n                            ) : (\r\n                                'Xác nhận hủy'\r\n                            )}\r\n                        </button>\r\n                    </div>\r\n                </form>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default CancelAppointmentModal; "], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AALA;;;;;AAaA,MAAM,yBAAgE,CAAC,EACnE,WAAW,EACX,OAAO,EACP,SAAS,EACZ;IACG,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD;IAC5C,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAE1C,MAAM,eAAe,OAAO;QACxB,EAAE,cAAc;QAEhB,IAAI,CAAC,aAAa,IAAI,IAAI;YACtB,UAAU;YACV;QACJ;QAEA,WAAW;QAEX,IAAI;YACA,MAAM,SAAS,MAAM,kBAAkB,YAAY,aAAa,EAAE;YAElE,IAAI,OAAO,OAAO,EAAE;gBAChB,YAAY;gBACZ;YACJ,OAAO;gBACH,UAAU,OAAO,OAAO,IAAI;YAChC;QACJ,EAAE,OAAO,KAAK;YACV,UAAU;QACd,SAAU;YACN,WAAW;QACf;IACJ;IAEA,qBACI,8OAAC;QAAI,WAAU;kBACX,cAAA,8OAAC;YAAI,WAAU;;8BACX,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCACpD,8OAAC;4BACG,SAAS;4BACT,WAAU;4BACV,UAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CAC/D,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;8BAKjF,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCAAE,WAAU;;wCAAoC;wCAC3C,YAAY,iBAAiB;wCAAC;wCAAI,YAAY,MAAM,CAAC,QAAQ;;;;;;;8CAEnE,8OAAC;oCAAE,WAAU;;wCACR,YAAY,eAAe;wCAAC;wCAAI,YAAY,eAAe;;;;;;;;;;;;;;;;;;;8BAKxE,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACpC,8OAAC;;8CACG,8OAAC;oCAAM,SAAQ;oCAAe,WAAU;;wCAA+C;sDACzE,8OAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAE7C,8OAAC;oCACG,IAAG;oCACH,OAAO;oCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCAC/C,MAAM;oCACN,WAAU;oCACV,aAAY;oCACZ,UAAU;;;;;;;;;;;;sCAIlB,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCACG,MAAK;oCACL,SAAS;oCACT,UAAU;oCACV,WAAU;8CACb;;;;;;8CAGD,8OAAC;oCACG,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,wBACG,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAI,WAAU;gDAAuB,MAAK;gDAAO,SAAQ;;kEACtD,8OAAC;wDAAO,WAAU;wDAAa,IAAG;wDAAK,IAAG;wDAAK,GAAE;wDAAK,QAAO;wDAAe,aAAY;;;;;;kEACxF,8OAAC;wDAAK,WAAU;wDAAa,MAAK;wDAAe,GAAE;;;;;;;;;;;;4CACjD;;;;;;+CAIV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhC;uCAEe", "debugId": null}}, {"offset": {"line": 641, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/utils/tokenStorage.ts"], "sourcesContent": ["export const tokenStorage = {\r\n    getAccessToken: () => localStorage.getItem('accessToken'),\r\n    setAccessToken: (token: string) => localStorage.setItem('accessToken', token),\r\n    clearAccessToken: () => localStorage.removeItem('accessToken'),\r\n};"], "names": [], "mappings": ";;;AAAO,MAAM,eAAe;IACxB,gBAAgB,IAAM,aAAa,OAAO,CAAC;IAC3C,gBAAgB,CAAC,QAAkB,aAAa,OAAO,CAAC,eAAe;IACvE,kBAAkB,IAAM,aAAa,UAAU,CAAC;AACpD", "debugId": null}}, {"offset": {"line": 655, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/utils/interceptor.ts"], "sourcesContent": ["import { API_URL } from './baseUrl';\r\nimport { tokenStorage } from './tokenStorage';\r\nimport { redirect } from 'next/navigation';\r\n\r\ninterface CustomRequestInit extends RequestInit {\r\n    skipAuth?: boolean;\r\n}\r\n\r\nconst PUBLIC_ENDPOINTS = [\r\n    '/api/v1/auth/login',\r\n    '/api/v1/users',\r\n    '/api/v1/auth/forgot-password',\r\n    '/api/v1/auth/reset-password',\r\n    '/api/v1/auth/verify-email',\r\n];\r\n\r\nfunction isPublicEndpoint(url: string): boolean {\r\n    return PUBLIC_ENDPOINTS.some(endpoint => {\r\n        return url.includes(endpoint) || url.endsWith(endpoint);\r\n    });\r\n}\r\n\r\nlet refreshingPromise: Promise<boolean> | null = null;\r\n\r\nasync function refreshAccessToken(): Promise<boolean> {\r\n    const accessToken = tokenStorage.getAccessToken();\r\n    if (!accessToken) return false;\r\n\r\n    try {\r\n        const response = await fetch(`${API_URL}/api/v1/auth/refresh-token`, {\r\n            method: 'POST',\r\n            headers: {\r\n                'Authorization': `Bearer ${accessToken}`,\r\n                'Content-Type': 'application/json'\r\n            },\r\n            // credentials: 'include'\r\n        });\r\n\r\n        if (!response.ok) throw new Error('Refresh failed');\r\n\r\n        const data = await response.json();\r\n        tokenStorage.setAccessToken(data.data.accessToken);\r\n        return true;\r\n    } catch (error) {\r\n        return false;\r\n    } finally {\r\n        refreshingPromise = null;\r\n    }\r\n}\r\n\r\nexport const fetchInterceptor = async (url: string, options: CustomRequestInit = {}): Promise<Response> => {\r\n    const requestOptions: CustomRequestInit = {\r\n        ...options,\r\n        // credentials: 'include'\r\n    };\r\n\r\n    requestOptions.headers = {\r\n        'Content-Type': 'application/json',\r\n        ...requestOptions.headers,\r\n    };\r\n\r\n    const isPublic = options.skipAuth || isPublicEndpoint(url);\r\n\r\n    if (!isPublic) {\r\n        const token = tokenStorage.getAccessToken();\r\n        if (token) {\r\n            requestOptions.headers = {\r\n                ...requestOptions.headers,\r\n                Authorization: `Bearer ${token}`,\r\n            };\r\n        }\r\n    }\r\n\r\n    try {\r\n        let response = await fetch(url, requestOptions);\r\n\r\n        if (response.status === 401 && !requestOptions.skipAuth) {\r\n            if (!refreshingPromise) {\r\n                refreshingPromise = refreshAccessToken();\r\n            }\r\n            try {\r\n                await refreshingPromise;\r\n\r\n                requestOptions.headers = {\r\n                    ...requestOptions.headers,\r\n                    Authorization: `Bearer ${tokenStorage.getAccessToken()}`,\r\n                };\r\n\r\n                response = await fetch(url, requestOptions);\r\n            } catch (error) {\r\n                console.log('Token refresh failed:', error);\r\n                redirect('/login');\r\n            }\r\n        }\r\n\r\n        if (!response.ok) {\r\n            const errorData = await response.json().catch(() => ({}));\r\n            throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\r\n        }\r\n\r\n        return response;\r\n\r\n    } catch (error) {\r\n        if (error instanceof Error) {\r\n            throw error;\r\n        }\r\n        throw new Error('Network error occurred');\r\n    }\r\n};"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAMA,MAAM,mBAAmB;IACrB;IACA;IACA;IACA;IACA;CACH;AAED,SAAS,iBAAiB,GAAW;IACjC,OAAO,iBAAiB,IAAI,CAAC,CAAA;QACzB,OAAO,IAAI,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC;IAClD;AACJ;AAEA,IAAI,oBAA6C;AAEjD,eAAe;IACX,MAAM,cAAc,4HAAA,CAAA,eAAY,CAAC,cAAc;IAC/C,IAAI,CAAC,aAAa,OAAO;IAEzB,IAAI;QACA,MAAM,WAAW,MAAM,MAAM,GAAG,uHAAA,CAAA,UAAO,CAAC,0BAA0B,CAAC,EAAE;YACjE,QAAQ;YACR,SAAS;gBACL,iBAAiB,CAAC,OAAO,EAAE,aAAa;gBACxC,gBAAgB;YACpB;QAEJ;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;QAElC,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,4HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,KAAK,IAAI,CAAC,WAAW;QACjD,OAAO;IACX,EAAE,OAAO,OAAO;QACZ,OAAO;IACX,SAAU;QACN,oBAAoB;IACxB;AACJ;AAEO,MAAM,mBAAmB,OAAO,KAAa,UAA6B,CAAC,CAAC;IAC/E,MAAM,iBAAoC;QACtC,GAAG,OAAO;IAEd;IAEA,eAAe,OAAO,GAAG;QACrB,gBAAgB;QAChB,GAAG,eAAe,OAAO;IAC7B;IAEA,MAAM,WAAW,QAAQ,QAAQ,IAAI,iBAAiB;IAEtD,IAAI,CAAC,UAAU;QACX,MAAM,QAAQ,4HAAA,CAAA,eAAY,CAAC,cAAc;QACzC,IAAI,OAAO;YACP,eAAe,OAAO,GAAG;gBACrB,GAAG,eAAe,OAAO;gBACzB,eAAe,CAAC,OAAO,EAAE,OAAO;YACpC;QACJ;IACJ;IAEA,IAAI;QACA,IAAI,WAAW,MAAM,MAAM,KAAK;QAEhC,IAAI,SAAS,MAAM,KAAK,OAAO,CAAC,eAAe,QAAQ,EAAE;YACrD,IAAI,CAAC,mBAAmB;gBACpB,oBAAoB;YACxB;YACA,IAAI;gBACA,MAAM;gBAEN,eAAe,OAAO,GAAG;oBACrB,GAAG,eAAe,OAAO;oBACzB,eAAe,CAAC,OAAO,EAAE,4HAAA,CAAA,eAAY,CAAC,cAAc,IAAI;gBAC5D;gBAEA,WAAW,MAAM,MAAM,KAAK;YAChC,EAAE,OAAO,OAAO;gBACZ,QAAQ,GAAG,CAAC,yBAAyB;gBACrC,CAAA,GAAA,kIAAA,CAAA,WAAQ,AAAD,EAAE;YACb;QACJ;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YACd,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;YACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QACjF;QAEA,OAAO;IAEX,EAAE,OAAO,OAAO;QACZ,IAAI,iBAAiB,OAAO;YACxB,MAAM;QACV;QACA,MAAM,IAAI,MAAM;IACpB;AACJ", "debugId": null}}, {"offset": {"line": 752, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/services/doctorService.ts"], "sourcesContent": ["import { ApiResponse } from \"@/types/apiResonse\";\r\nimport { <PERSON><PERSON><PERSON><PERSON>Request, DoctorCreationR<PERSON>ponse, Doctor<PERSON><PERSON>il<PERSON><PERSON><PERSON><PERSON>, DoctorSearchResponse, Gender } from \"@/types/doctor\";\r\nimport { PageResponse } from \"@/types/pageResponse\";\r\nimport { API_URL } from \"@/utils/baseUrl\";\r\nimport { fetchInterceptor } from \"@/utils/interceptor\";\r\n\r\nexport interface SearchDoctorsParams {\r\n    doctorName?: string;\r\n    specialtyName?: string;\r\n    gender?: Gender;\r\n    isAvailable?: boolean;\r\n    orderBy?: string;\r\n    page?: number;\r\n    pageSize?: number;\r\n}\r\n\r\nconst accessToken = localStorage.getItem('accessToken');\r\n\r\nexport const doctorService = {\r\n    async searchDoctors(params: SearchDoctorsParams = {}): Promise<ApiResponse<PageResponse<DoctorSearchResponse>>> {\r\n        const queryParams = new URLSearchParams();\r\n\r\n        if (params.doctorName) queryParams.append('doctorName', params.doctorName);\r\n        if (params.specialtyName) queryParams.append('specialtyName', params.specialtyName);\r\n        if (params.gender) queryParams.append('gender', params.gender);\r\n        if (params.isAvailable !== undefined) queryParams.append('isAvailable', params.isAvailable.toString());\r\n        if (params.orderBy) queryParams.append('orderBy', params.orderBy);\r\n        if (params.page) queryParams.append('page', params.page.toString());\r\n        if (params.pageSize) queryParams.append('pageSize', params.pageSize.toString());\r\n\r\n        const url = `${API_URL}/api/v1/doctors/search?${queryParams.toString()}`;\r\n        console.log('🔍 Debug - Search Doctors URL:', url);\r\n        console.log('🔍 Debug - Search Parameters:', params);\r\n\r\n        const response = await fetch(url, {\r\n            method: 'GET',\r\n            headers: {\r\n                'Content-Type': 'application/json',\r\n            },\r\n        });\r\n\r\n        console.log('🔍 Debug - Response Status:', response.status);\r\n        console.log('🔍 Debug - Response OK:', response.ok);\r\n\r\n        if (!response.ok) {\r\n            throw new Error(`HTTP error! status: ${response.status}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        console.log('🔍 Debug - Response Data:', data);\r\n        return data;\r\n    },\r\n\r\n    async getDoctorDetails(doctorId: number): Promise<ApiResponse<DoctorDetailResponse>> {\r\n        const url = `${API_URL}/api/v1/doctors/${doctorId}`;\r\n\r\n        const response = await fetch(url, {\r\n            method: 'GET',\r\n            headers: {\r\n                'Content-Type': 'application/json',\r\n            },\r\n        });\r\n\r\n        console.log('🔍 Debug - Doctor Details Response Status:', response.status);\r\n\r\n        if (!response.ok) {\r\n            throw new Error(`HTTP error! status: ${response.status}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        console.log('🔍 Debug - Doctor Details Response Data:', data);\r\n        return data;\r\n    },\r\n\r\n    async getDoctorAppointmentSchedule(doctorId: number, fromDate?: string, toDate?: string) {\r\n        const params = new URLSearchParams();\r\n        if (fromDate) params.append('fromDate', fromDate);\r\n        if (toDate) params.append('toDate', toDate);\r\n        const url = `${API_URL}/api/v1/doctors/${doctorId}/schedule?${params.toString()}`;\r\n        const response = await fetch(url, {\r\n            method: 'GET',\r\n            headers: {\r\n                'Content-Type': 'application/json',\r\n            },\r\n        });\r\n        if (!response.ok) {\r\n            throw new Error(`HTTP error! status: ${response.status}`);\r\n        }\r\n        return response.json();\r\n    },\r\n\r\n    async getDoctorWorkingSchedule(doctorId: number, daysAhead: number = 14) {\r\n        const params = new URLSearchParams();\r\n        params.append('daysAhead', daysAhead.toString());\r\n        const url = `${API_URL}/api/v1/doctors/${doctorId}/working-schedule?${params.toString()}`;\r\n        const response = await fetch(url, {\r\n            method: 'GET',\r\n            headers: {\r\n                'Content-Type': 'application/json',\r\n            },\r\n        });\r\n        if (!response.ok) {\r\n            throw new Error(`HTTP error! status: ${response.status}`);\r\n        }\r\n        return response.json();\r\n    }\r\n};\r\n\r\nexport const getDoctors = async (params: SearchDoctorsParams = {}): Promise<ApiResponse<PageResponse<DoctorDetailResponse>>> => {\r\n    const queryParams = new URLSearchParams();\r\n\r\n    if (params.doctorName) queryParams.append('doctorName', params.doctorName);\r\n    if (params.specialtyName) queryParams.append('specialtyName', params.specialtyName);\r\n    if (params.gender) queryParams.append('gender', params.gender);\r\n    if (params.isAvailable !== undefined) queryParams.append('isAvailable', params.isAvailable.toString());\r\n    if (params.orderBy) queryParams.append('orderBy', params.orderBy);\r\n    if (params.page) queryParams.append('page', params.page.toString());\r\n    if (params.pageSize) queryParams.append('pageSize', params.pageSize.toString());\r\n\r\n    const url = `${API_URL}/api/v1/doctors?${queryParams.toString()}`;\r\n    console.log('🔍 Debug - Search Doctors URL:', url);\r\n    console.log('🔍 Debug - Search Parameters:', params);\r\n\r\n    const response = await fetch(url, {\r\n        method: 'GET',\r\n        headers: {\r\n            'Content-Type': 'application/json',\r\n            ...(accessToken && { 'Authorization': `Bearer ${accessToken}` }),\r\n        },\r\n    });\r\n\r\n    console.log('🔍 Debug - Response Status:', response.status);\r\n    console.log('🔍 Debug - Response OK:', response.ok);\r\n\r\n    if (!response.ok) {\r\n        throw new Error(`HTTP error! status: ${response.status}`);\r\n    }\r\n\r\n    const data = await response.json();\r\n    console.log('🔍 Debug - Response Data:', data);\r\n    return data;\r\n};\r\n\r\n\r\nexport const createDoctor = async (data: DoctorCreationRequest): Promise<ApiResponse<DoctorCreationResponse>> => {\r\n    const response = await fetchInterceptor(`${API_URL}/api/v1/doctors`, {\r\n        method: \"POST\",\r\n        body: JSON.stringify(data)\r\n    });\r\n\r\n    const result: ApiResponse<DoctorCreationResponse> = await response.json();\r\n    return result;\r\n};\r\n\r\nexport const getDoctorWorkingSlots = async (doctorId: number, workDate?: string): Promise<ApiResponse<object>> => {\r\n    const params = new URLSearchParams();\r\n    if (workDate) params.append('fromDate', workDate);\r\n    const url = `${API_URL}/api/v1/doctors/${doctorId}/slot-day?${params.toString()}`;\r\n    const response = await fetch(url, {\r\n        method: 'GET',\r\n        headers: {\r\n            'Content-Type': 'application/json',\r\n        },\r\n    });\r\n    if (!response.ok) {\r\n        throw new Error(`HTTP error! status: ${response.status}`);\r\n    }\r\n    const result: ApiResponse<object> = await response.json();\r\n    return result;\r\n} \r\n"], "names": [], "mappings": ";;;;;;AAGA;AACA;;;AAYA,MAAM,cAAc,aAAa,OAAO,CAAC;AAElC,MAAM,gBAAgB;IACzB,MAAM,eAAc,SAA8B,CAAC,CAAC;QAChD,MAAM,cAAc,IAAI;QAExB,IAAI,OAAO,UAAU,EAAE,YAAY,MAAM,CAAC,cAAc,OAAO,UAAU;QACzE,IAAI,OAAO,aAAa,EAAE,YAAY,MAAM,CAAC,iBAAiB,OAAO,aAAa;QAClF,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC7D,IAAI,OAAO,WAAW,KAAK,WAAW,YAAY,MAAM,CAAC,eAAe,OAAO,WAAW,CAAC,QAAQ;QACnG,IAAI,OAAO,OAAO,EAAE,YAAY,MAAM,CAAC,WAAW,OAAO,OAAO;QAChE,IAAI,OAAO,IAAI,EAAE,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAChE,IAAI,OAAO,QAAQ,EAAE,YAAY,MAAM,CAAC,YAAY,OAAO,QAAQ,CAAC,QAAQ;QAE5E,MAAM,MAAM,GAAG,uHAAA,CAAA,UAAO,CAAC,uBAAuB,EAAE,YAAY,QAAQ,IAAI;QACxE,QAAQ,GAAG,CAAC,kCAAkC;QAC9C,QAAQ,GAAG,CAAC,iCAAiC;QAE7C,MAAM,WAAW,MAAM,MAAM,KAAK;YAC9B,QAAQ;YACR,SAAS;gBACL,gBAAgB;YACpB;QACJ;QAEA,QAAQ,GAAG,CAAC,+BAA+B,SAAS,MAAM;QAC1D,QAAQ,GAAG,CAAC,2BAA2B,SAAS,EAAE;QAElD,IAAI,CAAC,SAAS,EAAE,EAAE;YACd,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC5D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,6BAA6B;QACzC,OAAO;IACX;IAEA,MAAM,kBAAiB,QAAgB;QACnC,MAAM,MAAM,GAAG,uHAAA,CAAA,UAAO,CAAC,gBAAgB,EAAE,UAAU;QAEnD,MAAM,WAAW,MAAM,MAAM,KAAK;YAC9B,QAAQ;YACR,SAAS;gBACL,gBAAgB;YACpB;QACJ;QAEA,QAAQ,GAAG,CAAC,8CAA8C,SAAS,MAAM;QAEzE,IAAI,CAAC,SAAS,EAAE,EAAE;YACd,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC5D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,4CAA4C;QACxD,OAAO;IACX;IAEA,MAAM,8BAA6B,QAAgB,EAAE,QAAiB,EAAE,MAAe;QACnF,MAAM,SAAS,IAAI;QACnB,IAAI,UAAU,OAAO,MAAM,CAAC,YAAY;QACxC,IAAI,QAAQ,OAAO,MAAM,CAAC,UAAU;QACpC,MAAM,MAAM,GAAG,uHAAA,CAAA,UAAO,CAAC,gBAAgB,EAAE,SAAS,UAAU,EAAE,OAAO,QAAQ,IAAI;QACjF,MAAM,WAAW,MAAM,MAAM,KAAK;YAC9B,QAAQ;YACR,SAAS;gBACL,gBAAgB;YACpB;QACJ;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YACd,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC5D;QACA,OAAO,SAAS,IAAI;IACxB;IAEA,MAAM,0BAAyB,QAAgB,EAAE,YAAoB,EAAE;QACnE,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,CAAC,aAAa,UAAU,QAAQ;QAC7C,MAAM,MAAM,GAAG,uHAAA,CAAA,UAAO,CAAC,gBAAgB,EAAE,SAAS,kBAAkB,EAAE,OAAO,QAAQ,IAAI;QACzF,MAAM,WAAW,MAAM,MAAM,KAAK;YAC9B,QAAQ;YACR,SAAS;gBACL,gBAAgB;YACpB;QACJ;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YACd,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC5D;QACA,OAAO,SAAS,IAAI;IACxB;AACJ;AAEO,MAAM,aAAa,OAAO,SAA8B,CAAC,CAAC;IAC7D,MAAM,cAAc,IAAI;IAExB,IAAI,OAAO,UAAU,EAAE,YAAY,MAAM,CAAC,cAAc,OAAO,UAAU;IACzE,IAAI,OAAO,aAAa,EAAE,YAAY,MAAM,CAAC,iBAAiB,OAAO,aAAa;IAClF,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;IAC7D,IAAI,OAAO,WAAW,KAAK,WAAW,YAAY,MAAM,CAAC,eAAe,OAAO,WAAW,CAAC,QAAQ;IACnG,IAAI,OAAO,OAAO,EAAE,YAAY,MAAM,CAAC,WAAW,OAAO,OAAO;IAChE,IAAI,OAAO,IAAI,EAAE,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;IAChE,IAAI,OAAO,QAAQ,EAAE,YAAY,MAAM,CAAC,YAAY,OAAO,QAAQ,CAAC,QAAQ;IAE5E,MAAM,MAAM,GAAG,uHAAA,CAAA,UAAO,CAAC,gBAAgB,EAAE,YAAY,QAAQ,IAAI;IACjE,QAAQ,GAAG,CAAC,kCAAkC;IAC9C,QAAQ,GAAG,CAAC,iCAAiC;IAE7C,MAAM,WAAW,MAAM,MAAM,KAAK;QAC9B,QAAQ;QACR,SAAS;YACL,gBAAgB;YAChB,GAAI,eAAe;gBAAE,iBAAiB,CAAC,OAAO,EAAE,aAAa;YAAC,CAAC;QACnE;IACJ;IAEA,QAAQ,GAAG,CAAC,+BAA+B,SAAS,MAAM;IAC1D,QAAQ,GAAG,CAAC,2BAA2B,SAAS,EAAE;IAElD,IAAI,CAAC,SAAS,EAAE,EAAE;QACd,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;IAC5D;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAChC,QAAQ,GAAG,CAAC,6BAA6B;IACzC,OAAO;AACX;AAGO,MAAM,eAAe,OAAO;IAC/B,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,mBAAgB,AAAD,EAAE,GAAG,uHAAA,CAAA,UAAO,CAAC,eAAe,CAAC,EAAE;QACjE,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACzB;IAEA,MAAM,SAA8C,MAAM,SAAS,IAAI;IACvE,OAAO;AACX;AAEO,MAAM,wBAAwB,OAAO,UAAkB;IAC1D,MAAM,SAAS,IAAI;IACnB,IAAI,UAAU,OAAO,MAAM,CAAC,YAAY;IACxC,MAAM,MAAM,GAAG,uHAAA,CAAA,UAAO,CAAC,gBAAgB,EAAE,SAAS,UAAU,EAAE,OAAO,QAAQ,IAAI;IACjF,MAAM,WAAW,MAAM,MAAM,KAAK;QAC9B,QAAQ;QACR,SAAS;YACL,gBAAgB;QACpB;IACJ;IACA,IAAI,CAAC,SAAS,EAAE,EAAE;QACd,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;IAC5D;IACA,MAAM,SAA8B,MAAM,SAAS,IAAI;IACvD,OAAO;AACX", "debugId": null}}, {"offset": {"line": 899, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/patient/AppointmentHistory/RescheduleAppointmentModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport { AppointmentResponse } from '@/types/appointment';\r\nimport { useAppointments } from '@/hooks/useAppointments';\r\nimport { useToast } from '@/hooks/useToast';\r\nimport { doctorService } from '@/services/doctorService';\r\n\r\ninterface RescheduleAppointmentModalProps {\r\n    appointment: AppointmentResponse;\r\n    onClose: () => void;\r\n    onSuccess: () => void;\r\n}\r\n\r\nconst RescheduleAppointmentModal: React.FC<RescheduleAppointmentModalProps> = ({\r\n    appointment,\r\n    onClose,\r\n    onSuccess\r\n}) => {\r\n    const [newDate, setNewDate] = useState('');\r\n    const [newSlotId, setNewSlotId] = useState<number | null>(null);\r\n    const [reason, setReason] = useState('');\r\n    const [loading, setLoading] = useState(false);\r\n    const [schedule, setSchedule] = useState<any[]>([]);\r\n    const { rescheduleAppointment } = useAppointments();\r\n    const { showSuccess, showError } = useToast();\r\n\r\n    // Fetch available schedule for the doctor\r\n    React.useEffect(() => {\r\n        const fetchSchedule = async () => {\r\n            try {\r\n                alert(appointment.doctor.doctorId);\r\n                const res = await doctorService.getDoctorAppointmentSchedule(appointment.doctor.doctorId);\r\n                setSchedule(res.result?.workSchedules || []);\r\n            } catch (err) {\r\n                setSchedule([]);\r\n            }\r\n        };\r\n        fetchSchedule();\r\n    }, [appointment.doctor.doctorId]);\r\n\r\n    // Lấy danh sách ngày có slot rảnh\r\n    const availableDays = schedule.filter((day) => day.availableSlots.some((slot: any) => slot.isAvailable));\r\n    // Lấy danh sách slot rảnh cho ngày đã chọn\r\n    const availableSlots = schedule.find((d) => d.workDate.split('T')[0] === newDate)?.availableSlots.filter((slot: any) => slot.isAvailable) || [];\r\n\r\n    const handleSubmit = async (e: React.FormEvent) => {\r\n        e.preventDefault();\r\n        if (!newDate || !newSlotId || !reason.trim()) {\r\n            showError('Vui lòng điền đầy đủ thông tin');\r\n            return;\r\n        }\r\n        setLoading(true);\r\n        try {\r\n            const result = await rescheduleAppointment(\r\n                appointment.appointmentId,\r\n                newSlotId,\r\n                newDate,\r\n                reason\r\n            );\r\n            if (result.success) {\r\n                showSuccess('Đổi lịch hẹn thành công');\r\n                onSuccess();\r\n            } else {\r\n                showError(result.message || 'Có lỗi xảy ra khi đổi lịch hẹn');\r\n            }\r\n        } catch (err) {\r\n            showError('Có lỗi xảy ra khi đổi lịch hẹn');\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    // Get minimum date (tomorrow)\r\n    const tomorrow = new Date();\r\n    tomorrow.setDate(tomorrow.getDate() + 1);\r\n    const minDate = tomorrow.toISOString().split('T')[0];\r\n\r\n    return (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n            <div className=\"bg-white rounded-lg max-w-md w-full p-6\">\r\n                <div className=\"flex items-center justify-between mb-4\">\r\n                    <h3 className=\"text-lg font-semibold text-gray-900\">Đổi lịch hẹn</h3>\r\n                    <button\r\n                        onClick={onClose}\r\n                        className=\"text-gray-400 hover:text-gray-600\"\r\n                        disabled={loading}\r\n                    >\r\n                        <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                        </svg>\r\n                    </button>\r\n                </div>\r\n\r\n                <div className=\"mb-4\">\r\n                    <p className=\"text-gray-600 mb-2\">\r\n                        Thông tin lịch hẹn hiện tại:\r\n                    </p>\r\n                    <div className=\"bg-gray-50 p-3 rounded-md\">\r\n                        <p className=\"text-sm font-medium text-gray-900\">\r\n                            #{appointment.appointmentNumber} - {appointment.doctor.fullName}\r\n                        </p>\r\n                        <p className=\"text-sm text-gray-600\">\r\n                            {appointment.appointmentDate} - {appointment.appointmentTime}\r\n                        </p>\r\n                    </div>\r\n                </div>\r\n\r\n                <form onSubmit={handleSubmit} className=\"space-y-4\">\r\n                    <div>\r\n                        <label htmlFor=\"newDate\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                            Ngày mới <span className=\"text-red-500\">*</span>\r\n                        </label>\r\n                        <select\r\n                            id=\"newDate\"\r\n                            value={newDate}\r\n                            onChange={(e) => {\r\n                                setNewDate(e.target.value);\r\n                                setNewSlotId(null);\r\n                            }}\r\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                            disabled={loading}\r\n                        >\r\n                            <option value=\"\">Chọn ngày</option>\r\n                            {availableDays.map((day: any) => (\r\n                                <option key={day.workDate} value={day.workDate.split('T')[0]}>\r\n                                    {new Date(day.workDate).toLocaleDateString()}\r\n                                </option>\r\n                            ))}\r\n                        </select>\r\n                    </div>\r\n\r\n                    <div>\r\n                        <label htmlFor=\"newSlotId\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                            Giờ mới <span className=\"text-red-500\">*</span>\r\n                        </label>\r\n                        <select\r\n                            id=\"newSlotId\"\r\n                            value={newSlotId || ''}\r\n                            onChange={(e) => setNewSlotId(Number(e.target.value))}\r\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                            disabled={loading || !newDate}\r\n                        >\r\n                            <option value=\"\">Chọn giờ</option>\r\n                            {availableSlots.map((slot: any) => (\r\n                                <option key={slot.slotId} value={slot.slotId}>\r\n                                    {slot.slotTimeFormatted}\r\n                                </option>\r\n                            ))}\r\n                        </select>\r\n                    </div>\r\n\r\n                    <div>\r\n                        <label htmlFor=\"reason\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                            Lý do đổi lịch <span className=\"text-red-500\">*</span>\r\n                        </label>\r\n                        <textarea\r\n                            id=\"reason\"\r\n                            value={reason}\r\n                            onChange={(e) => setReason(e.target.value)}\r\n                            rows={3}\r\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                            placeholder=\"Nhập lý do đổi lịch hẹn...\"\r\n                            disabled={loading}\r\n                        />\r\n                    </div>\r\n\r\n                    <div className=\"flex gap-3\">\r\n                        <button\r\n                            type=\"button\"\r\n                            onClick={onClose}\r\n                            disabled={loading}\r\n                            className=\"flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\r\n                        >\r\n                            Hủy\r\n                        </button>\r\n                        <button\r\n                            type=\"submit\"\r\n                            disabled={loading}\r\n                            className=\"flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\r\n                        >\r\n                            {loading ? (\r\n                                <div className=\"flex items-center justify-center gap-2\">\r\n                                    <svg className=\"animate-spin h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                                        <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" />\r\n                                        <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\" />\r\n                                    </svg>\r\n                                    Đang xử lý...\r\n                                </div>\r\n                            ) : (\r\n                                'Xác nhận đổi lịch'\r\n                            )}\r\n                        </button>\r\n                    </div>\r\n                </form>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default RescheduleAppointmentModal; "], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AANA;;;;;;AAcA,MAAM,6BAAwE,CAAC,EAC3E,WAAW,EACX,OAAO,EACP,SAAS,EACZ;IACG,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAClD,MAAM,EAAE,qBAAqB,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD;IAChD,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAE1C,0CAA0C;IAC1C,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,MAAM,gBAAgB;YAClB,IAAI;gBACA,MAAM,YAAY,MAAM,CAAC,QAAQ;gBACjC,MAAM,MAAM,MAAM,gIAAA,CAAA,gBAAa,CAAC,4BAA4B,CAAC,YAAY,MAAM,CAAC,QAAQ;gBACxF,YAAY,IAAI,MAAM,EAAE,iBAAiB,EAAE;YAC/C,EAAE,OAAO,KAAK;gBACV,YAAY,EAAE;YAClB;QACJ;QACA;IACJ,GAAG;QAAC,YAAY,MAAM,CAAC,QAAQ;KAAC;IAEhC,kCAAkC;IAClC,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAC,MAAQ,IAAI,cAAc,CAAC,IAAI,CAAC,CAAC,OAAc,KAAK,WAAW;IACtG,2CAA2C;IAC3C,MAAM,iBAAiB,SAAS,IAAI,CAAC,CAAC,IAAM,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK,UAAU,eAAe,OAAO,CAAC,OAAc,KAAK,WAAW,KAAK,EAAE;IAE/I,MAAM,eAAe,OAAO;QACxB,EAAE,cAAc;QAChB,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,OAAO,IAAI,IAAI;YAC1C,UAAU;YACV;QACJ;QACA,WAAW;QACX,IAAI;YACA,MAAM,SAAS,MAAM,sBACjB,YAAY,aAAa,EACzB,WACA,SACA;YAEJ,IAAI,OAAO,OAAO,EAAE;gBAChB,YAAY;gBACZ;YACJ,OAAO;gBACH,UAAU,OAAO,OAAO,IAAI;YAChC;QACJ,EAAE,OAAO,KAAK;YACV,UAAU;QACd,SAAU;YACN,WAAW;QACf;IACJ;IAEA,8BAA8B;IAC9B,MAAM,WAAW,IAAI;IACrB,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK;IACtC,MAAM,UAAU,SAAS,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IAEpD,qBACI,8OAAC;QAAI,WAAU;kBACX,cAAA,8OAAC;YAAI,WAAU;;8BACX,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCACpD,8OAAC;4BACG,SAAS;4BACT,WAAU;4BACV,UAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CAC/D,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;8BAKjF,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCAAE,WAAU;;wCAAoC;wCAC3C,YAAY,iBAAiB;wCAAC;wCAAI,YAAY,MAAM,CAAC,QAAQ;;;;;;;8CAEnE,8OAAC;oCAAE,WAAU;;wCACR,YAAY,eAAe;wCAAC;wCAAI,YAAY,eAAe;;;;;;;;;;;;;;;;;;;8BAKxE,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACpC,8OAAC;;8CACG,8OAAC;oCAAM,SAAQ;oCAAU,WAAU;;wCAA+C;sDACrE,8OAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAE5C,8OAAC;oCACG,IAAG;oCACH,OAAO;oCACP,UAAU,CAAC;wCACP,WAAW,EAAE,MAAM,CAAC,KAAK;wCACzB,aAAa;oCACjB;oCACA,WAAU;oCACV,UAAU;;sDAEV,8OAAC;4CAAO,OAAM;sDAAG;;;;;;wCAChB,cAAc,GAAG,CAAC,CAAC,oBAChB,8OAAC;gDAA0B,OAAO,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;0DACvD,IAAI,KAAK,IAAI,QAAQ,EAAE,kBAAkB;+CADjC,IAAI,QAAQ;;;;;;;;;;;;;;;;;sCAOrC,8OAAC;;8CACG,8OAAC;oCAAM,SAAQ;oCAAY,WAAU;;wCAA+C;sDACxE,8OAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAE3C,8OAAC;oCACG,IAAG;oCACH,OAAO,aAAa;oCACpB,UAAU,CAAC,IAAM,aAAa,OAAO,EAAE,MAAM,CAAC,KAAK;oCACnD,WAAU;oCACV,UAAU,WAAW,CAAC;;sDAEtB,8OAAC;4CAAO,OAAM;sDAAG;;;;;;wCAChB,eAAe,GAAG,CAAC,CAAC,qBACjB,8OAAC;gDAAyB,OAAO,KAAK,MAAM;0DACvC,KAAK,iBAAiB;+CADd,KAAK,MAAM;;;;;;;;;;;;;;;;;sCAOpC,8OAAC;;8CACG,8OAAC;oCAAM,SAAQ;oCAAS,WAAU;;wCAA+C;sDAC9D,8OAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAElD,8OAAC;oCACG,IAAG;oCACH,OAAO;oCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;oCACzC,MAAM;oCACN,WAAU;oCACV,aAAY;oCACZ,UAAU;;;;;;;;;;;;sCAIlB,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCACG,MAAK;oCACL,SAAS;oCACT,UAAU;oCACV,WAAU;8CACb;;;;;;8CAGD,8OAAC;oCACG,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,wBACG,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAI,WAAU;gDAAuB,MAAK;gDAAO,SAAQ;;kEACtD,8OAAC;wDAAO,WAAU;wDAAa,IAAG;wDAAK,IAAG;wDAAK,GAAE;wDAAK,QAAO;wDAAe,aAAY;;;;;;kEACxF,8OAAC;wDAAK,WAAU;wDAAa,MAAK;wDAAe,GAAE;;;;;;;;;;;;4CACjD;;;;;;+CAIV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhC;uCAEe", "debugId": null}}, {"offset": {"line": 1332, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/patient/AppointmentHistory/AppointmentCard.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport { AppointmentResponse } from '@/types/appointment';\r\nimport { formatDateString } from '@/utils/dateUtils';\r\nimport { CancelAppointmentModal, RescheduleAppointmentModal } from './index';\r\n\r\ninterface AppointmentCardProps {\r\n    appointment: AppointmentResponse;\r\n    onRefresh: () => void;\r\n}\r\n\r\nconst AppointmentCard: React.FC<AppointmentCardProps> = ({ appointment, onRefresh }) => {\r\n    const [showCancelModal, setShowCancelModal] = useState(false);\r\n    const [showRescheduleModal, setShowRescheduleModal] = useState(false);\r\n\r\n    const getStatusColor = (status: string) => {\r\n        switch (status) {\r\n            case 'confirmed':\r\n                return 'bg-green-100 text-green-800';\r\n            case 'pending':\r\n                return 'bg-yellow-100 text-yellow-800';\r\n            case 'completed':\r\n                return 'bg-blue-100 text-blue-800';\r\n            case 'cancelled':\r\n                return 'bg-red-100 text-red-800';\r\n            case 'rescheduled':\r\n                return 'bg-purple-100 text-purple-800';\r\n            default:\r\n                return 'bg-gray-100 text-gray-800';\r\n        }\r\n    };\r\n\r\n    const getStatusText = (status: string) => {\r\n        switch (status) {\r\n            case 'confirmed':\r\n                return 'Đã xác nhận';\r\n            case 'pending':\r\n                return 'Chờ xác nhận';\r\n            case 'completed':\r\n                return 'Đã hoàn thành';\r\n            case 'cancelled':\r\n                return 'Đã hủy';\r\n            case 'rescheduled':\r\n                return 'Đã đổi lịch';\r\n            default:\r\n                return status;\r\n        }\r\n    };\r\n\r\n    const formatCurrency = (amount: number) => {\r\n        return new Intl.NumberFormat('vi-VN', {\r\n            style: 'currency',\r\n            currency: 'VND'\r\n        }).format(amount);\r\n    };\r\n\r\n    return (\r\n        <>\r\n            <div className=\"bg-white rounded-lg shadow-md border border-gray-200 p-6 hover:shadow-lg transition-shadow\">\r\n                {/* Header */}\r\n                <div className=\"flex items-start justify-between mb-4\">\r\n                    <div className=\"flex-1\">\r\n                        <div className=\"flex items-center gap-3 mb-2\">\r\n                            <h3 className=\"text-lg font-semibold text-gray-900\">\r\n                                #{appointment.appointmentNumber}\r\n                            </h3>\r\n                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(appointment.status)}`}>\r\n                                {getStatusText(appointment.status)}\r\n                            </span>\r\n                        </div>\r\n                        <p className=\"text-sm text-gray-600\">\r\n                            {formatDateString(appointment.appointmentDate)} - {appointment.appointmentTime}\r\n                        </p>\r\n                    </div>\r\n                    <div className=\"text-right\">\r\n                        <p className=\"text-lg font-semibold text-blue-600\">\r\n                            {formatCurrency(appointment.totalFee)}\r\n                        </p>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Doctor Info */}\r\n                <div className=\"border-t border-gray-100 pt-4 mb-4\">\r\n                    <div className=\"flex items-center gap-3\">\r\n                        <div className=\"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center\">\r\n                            <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\r\n                            </svg>\r\n                        </div>\r\n                        <div className=\"flex-1\">\r\n                            <h4 className=\"font-medium text-gray-900\">{appointment.doctor.fullName}</h4>\r\n                            <p className=\"text-sm text-gray-600\">{appointment.doctor.specialty}</p>\r\n                            <p className=\"text-xs text-gray-500\">\r\n                                {appointment.doctor.degree} • {appointment.doctor.yearsOfExperience} năm kinh nghiệm\r\n                            </p>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Reason for Visit */}\r\n                <div className=\"border-t border-gray-100 pt-4 mb-4\">\r\n                    <h5 className=\"font-medium text-gray-900 mb-2\">Lý do khám</h5>\r\n                    <p className=\"text-sm text-gray-600 bg-gray-50 p-3 rounded-md\">\r\n                        {appointment.reasonForVisit}\r\n                    </p>\r\n                </div>\r\n\r\n                {/* Actions */}\r\n                {appointment.status !== 'cancelled' && appointment.status !== 'completed' && (\r\n                    <div className=\"border-t border-gray-100 pt-4\">\r\n                        <div className=\"flex gap-2\">\r\n                            {appointment.canCancel && (\r\n                                <button\r\n                                    onClick={() => setShowCancelModal(true)}\r\n                                    className=\"flex-1 px-4 py-2 text-sm font-medium text-red-600 border border-red-300 rounded-md hover:bg-red-50 transition-colors\"\r\n                                >\r\n                                    Hủy lịch hẹn\r\n                                </button>\r\n                            )}\r\n                            {appointment.canReschedule && (\r\n                                <button\r\n                                    onClick={() => setShowRescheduleModal(true)}\r\n                                    className=\"flex-1 px-4 py-2 text-sm font-medium text-blue-600 border border-blue-300 rounded-md hover:bg-blue-50 transition-colors\"\r\n                                >\r\n                                    Đổi lịch hẹn\r\n                                </button>\r\n                            )}\r\n                        </div>\r\n                    </div>\r\n                )}\r\n            </div>\r\n\r\n            {/* Modals */}\r\n            {showCancelModal && (\r\n                <CancelAppointmentModal\r\n                    appointment={appointment}\r\n                    onClose={() => setShowCancelModal(false)}\r\n                    onSuccess={() => {\r\n                        setShowCancelModal(false);\r\n                        onRefresh();\r\n                    }}\r\n                />\r\n            )}\r\n\r\n            {showRescheduleModal && (\r\n                <RescheduleAppointmentModal\r\n                    appointment={appointment}\r\n                    onClose={() => setShowRescheduleModal(false)}\r\n                    onSuccess={() => {\r\n                        setShowRescheduleModal(false);\r\n                        onRefresh();\r\n                    }}\r\n                />\r\n            )}\r\n        </>\r\n    );\r\n};\r\n\r\nexport default AppointmentCard; "], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAAA;AAAA;AALA;;;;;AAYA,MAAM,kBAAkD,CAAC,EAAE,WAAW,EAAE,SAAS,EAAE;IAC/E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,MAAM,iBAAiB,CAAC;QACpB,OAAQ;YACJ,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX;gBACI,OAAO;QACf;IACJ;IAEA,MAAM,gBAAgB,CAAC;QACnB,OAAQ;YACJ,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX;gBACI,OAAO;QACf;IACJ;IAEA,MAAM,iBAAiB,CAAC;QACpB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YAClC,OAAO;YACP,UAAU;QACd,GAAG,MAAM,CAAC;IACd;IAEA,qBACI;;0BACI,8OAAC;gBAAI,WAAU;;kCAEX,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAG,WAAU;;oDAAsC;oDAC9C,YAAY,iBAAiB;;;;;;;0DAEnC,8OAAC;gDAAK,WAAW,CAAC,2CAA2C,EAAE,eAAe,YAAY,MAAM,GAAG;0DAC9F,cAAc,YAAY,MAAM;;;;;;;;;;;;kDAGzC,8OAAC;wCAAE,WAAU;;4CACR,CAAA,GAAA,yHAAA,CAAA,mBAAgB,AAAD,EAAE,YAAY,eAAe;4CAAE;4CAAI,YAAY,eAAe;;;;;;;;;;;;;0CAGtF,8OAAC;gCAAI,WAAU;0CACX,cAAA,8OAAC;oCAAE,WAAU;8CACR,eAAe,YAAY,QAAQ;;;;;;;;;;;;;;;;;kCAMhD,8OAAC;wBAAI,WAAU;kCACX,cAAA,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCAAI,WAAU;8CACX,cAAA,8OAAC;wCAAI,WAAU;wCAAwB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC7E,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAG7E,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;4CAAG,WAAU;sDAA6B,YAAY,MAAM,CAAC,QAAQ;;;;;;sDACtE,8OAAC;4CAAE,WAAU;sDAAyB,YAAY,MAAM,CAAC,SAAS;;;;;;sDAClE,8OAAC;4CAAE,WAAU;;gDACR,YAAY,MAAM,CAAC,MAAM;gDAAC;gDAAI,YAAY,MAAM,CAAC,iBAAiB;gDAAC;;;;;;;;;;;;;;;;;;;;;;;;kCAOpF,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,8OAAC;gCAAE,WAAU;0CACR,YAAY,cAAc;;;;;;;;;;;;oBAKlC,YAAY,MAAM,KAAK,eAAe,YAAY,MAAM,KAAK,6BAC1D,8OAAC;wBAAI,WAAU;kCACX,cAAA,8OAAC;4BAAI,WAAU;;gCACV,YAAY,SAAS,kBAClB,8OAAC;oCACG,SAAS,IAAM,mBAAmB;oCAClC,WAAU;8CACb;;;;;;gCAIJ,YAAY,aAAa,kBACtB,8OAAC;oCACG,SAAS,IAAM,uBAAuB;oCACtC,WAAU;8CACb;;;;;;;;;;;;;;;;;;;;;;;YAUpB,iCACG,8OAAC,kOAAA,CAAA,yBAAsB;gBACnB,aAAa;gBACb,SAAS,IAAM,mBAAmB;gBAClC,WAAW;oBACP,mBAAmB;oBACnB;gBACJ;;;;;;YAIP,qCACG,8OAAC,0OAAA,CAAA,6BAA0B;gBACvB,aAAa;gBACb,SAAS,IAAM,uBAAuB;gBACtC,WAAW;oBACP,uBAAuB;oBACvB;gBACJ;;;;;;;;AAKpB;uCAEe", "debugId": null}}, {"offset": {"line": 1645, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/patient/AppointmentHistory/AppointmentFilters.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\n\r\ninterface AppointmentFiltersProps {\r\n    activeFilter: 'all' | 'upcoming' | 'completed' | 'cancelled';\r\n    onFilterChange: (filter: 'all' | 'upcoming' | 'completed' | 'cancelled') => void;\r\n    searchTerm: string;\r\n    onSearchChange: (term: string) => void;\r\n    appointmentCount: number;\r\n}\r\n\r\nconst AppointmentFilters: React.FC<AppointmentFiltersProps> = ({\r\n    activeFilter,\r\n    onFilterChange,\r\n    searchTerm,\r\n    onSearchChange,\r\n    appointmentCount\r\n}) => {\r\n    const filters = [\r\n        { key: 'all', label: 'Tất cả', icon: '📋' },\r\n        { key: 'upcoming', label: 'Sắp tới', icon: '⏰' },\r\n        { key: 'completed', label: 'Đã hoàn thành', icon: '✅' },\r\n        { key: 'cancelled', label: 'Đã hủy', icon: '❌' }\r\n    ] as const;\r\n\r\n    return (\r\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-4\">\r\n            <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\">\r\n                {/* Search */}\r\n                <div className=\"flex-1 max-w-md\">\r\n                    <div className=\"relative\">\r\n                        <svg\r\n                            className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\"\r\n                            fill=\"none\"\r\n                            stroke=\"currentColor\"\r\n                            viewBox=\"0 0 24 24\"\r\n                        >\r\n                            <path\r\n                                strokeLinecap=\"round\"\r\n                                strokeLinejoin=\"round\"\r\n                                strokeWidth={2}\r\n                                d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\r\n                            />\r\n                        </svg>\r\n                        <input\r\n                            type=\"text\"\r\n                            placeholder=\"Tìm kiếm theo bác sĩ, mã cuộc hẹn...\"\r\n                            value={searchTerm}\r\n                            onChange={(e) => onSearchChange(e.target.value)}\r\n                            className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                        />\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Filter Tabs */}\r\n                <div className=\"flex items-center gap-2\">\r\n                    {filters.map((filter) => (\r\n                        <button\r\n                            key={filter.key}\r\n                            onClick={() => onFilterChange(filter.key)}\r\n                            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors flex items-center gap-2 ${\r\n                                activeFilter === filter.key\r\n                                    ? 'bg-blue-600 text-white'\r\n                                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\r\n                            }`}\r\n                        >\r\n                            <span>{filter.icon}</span>\r\n                            <span className=\"hidden sm:inline\">{filter.label}</span>\r\n                        </button>\r\n                    ))}\r\n                </div>\r\n            </div>\r\n\r\n            {/* Results Count */}\r\n            <div className=\"mt-4 pt-4 border-t border-gray-100\">\r\n                <p className=\"text-sm text-gray-600\">\r\n                    Hiển thị {appointmentCount} cuộc hẹn\r\n                </p>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default AppointmentFilters; "], "names": [], "mappings": ";;;;AAAA;;AAYA,MAAM,qBAAwD,CAAC,EAC3D,YAAY,EACZ,cAAc,EACd,UAAU,EACV,cAAc,EACd,gBAAgB,EACnB;IACG,MAAM,UAAU;QACZ;YAAE,KAAK;YAAO,OAAO;YAAU,MAAM;QAAK;QAC1C;YAAE,KAAK;YAAY,OAAO;YAAW,MAAM;QAAI;QAC/C;YAAE,KAAK;YAAa,OAAO;YAAiB,MAAM;QAAI;QACtD;YAAE,KAAK;YAAa,OAAO;YAAU,MAAM;QAAI;KAClD;IAED,qBACI,8OAAC;QAAI,WAAU;;0BACX,8OAAC;gBAAI,WAAU;;kCAEX,8OAAC;wBAAI,WAAU;kCACX,cAAA,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCACG,WAAU;oCACV,MAAK;oCACL,QAAO;oCACP,SAAQ;8CAER,cAAA,8OAAC;wCACG,eAAc;wCACd,gBAAe;wCACf,aAAa;wCACb,GAAE;;;;;;;;;;;8CAGV,8OAAC;oCACG,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,WAAU;;;;;;;;;;;;;;;;;kCAMtB,8OAAC;wBAAI,WAAU;kCACV,QAAQ,GAAG,CAAC,CAAC,uBACV,8OAAC;gCAEG,SAAS,IAAM,eAAe,OAAO,GAAG;gCACxC,WAAW,CAAC,mFAAmF,EAC3F,iBAAiB,OAAO,GAAG,GACrB,2BACA,+CACR;;kDAEF,8OAAC;kDAAM,OAAO,IAAI;;;;;;kDAClB,8OAAC;wCAAK,WAAU;kDAAoB,OAAO,KAAK;;;;;;;+BAT3C,OAAO,GAAG;;;;;;;;;;;;;;;;0BAgB/B,8OAAC;gBAAI,WAAU;0BACX,cAAA,8OAAC;oBAAE,WAAU;;wBAAwB;wBACvB;wBAAiB;;;;;;;;;;;;;;;;;;AAK/C;uCAEe", "debugId": null}}, {"offset": {"line": 1798, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/patient/AppointmentHistory/index.ts"], "sourcesContent": ["export { default as AppointmentHistory } from './AppointmentHistory';\r\nexport { default as AppointmentCard } from './AppointmentCard';\r\nexport { default as AppointmentFilters } from './AppointmentFilters';\r\nexport { default as CancelAppointmentModal } from './CancelAppointmentModal';\r\nexport { default as RescheduleAppointmentModal } from './RescheduleAppointmentModal'; "], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1848, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/ui/LoadingSpinner.tsx"], "sourcesContent": ["interface LoadingSpinnerProps {\r\n    size?: 'sm' | 'md' | 'lg';\r\n}\r\n\r\nexport const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ size = 'md' }) => {\r\n    const sizeClasses = {\r\n        sm: 'h-4 w-4',\r\n        md: 'h-8 w-8',\r\n        lg: 'h-12 w-12'\r\n    };\r\n\r\n    return (\r\n        <div className=\"flex justify-center items-center\">\r\n            <div className={`animate-spin rounded-full border-t-2 border-b-2 border-blue-600 ${sizeClasses[size]}`}></div>\r\n        </div>\r\n    );\r\n};"], "names": [], "mappings": ";;;;;AAIO,MAAM,iBAAgD,CAAC,EAAE,OAAO,IAAI,EAAE;IACzE,MAAM,cAAc;QAChB,IAAI;QACJ,IAAI;QACJ,IAAI;IACR;IAEA,qBACI,8OAAC;QAAI,WAAU;kBACX,cAAA,8OAAC;YAAI,WAAW,CAAC,gEAAgE,EAAE,WAAW,CAAC,KAAK,EAAE;;;;;;;;;;;AAGlH", "debugId": null}}, {"offset": {"line": 1880, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/ui/Toast.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\r\n\r\ninterface ToastProps {\r\n    message: string;\r\n    type: 'success' | 'error' | 'info';\r\n    show: boolean;\r\n    onClose: () => void;\r\n    duration?: number;\r\n}\r\n\r\nconst Toast: React.FC<ToastProps> = ({ \r\n    message, \r\n    type, \r\n    show, \r\n    onClose, \r\n    duration = 3000 \r\n}) => {\r\n    useEffect(() => {\r\n        if (show) {\r\n            const timer = setTimeout(() => {\r\n                onClose();\r\n            }, duration);\r\n            return () => clearTimeout(timer);\r\n        }\r\n    }, [show, duration, onClose]);\r\n\r\n    if (!show) return null;\r\n\r\n    const getIcon = () => {\r\n        switch (type) {\r\n            case 'success':\r\n                return (\r\n                    <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n                    </svg>\r\n                );\r\n            case 'error':\r\n                return (\r\n                    <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\" />\r\n                    </svg>\r\n                );\r\n            case 'info':\r\n                return (\r\n                    <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                    </svg>\r\n                );\r\n            default:\r\n                return (\r\n                    <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                    </svg>\r\n                );\r\n        }\r\n    };\r\n\r\n    const getStyles = () => {\r\n        switch (type) {\r\n            case 'success':\r\n                return 'bg-green-50 border-green-200 text-green-800';\r\n            case 'error':\r\n                return 'bg-red-50 border-red-200 text-red-800';\r\n            case 'info':\r\n                return 'bg-blue-50 border-blue-200 text-blue-800';\r\n            default:\r\n                return 'bg-gray-50 border-gray-200 text-gray-800';\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div className=\"fixed top-4 right-4 z-50 animate-in slide-in-from-top-2 duration-300\">\r\n            <div className={`flex items-center p-4 border rounded-lg shadow-lg max-w-md ${getStyles()}`}>\r\n                <div className=\"flex-shrink-0 mr-3\">\r\n                    {getIcon()}\r\n                </div>\r\n                <div className=\"flex-1\">\r\n                    <p className=\"text-sm font-medium\">{message}</p>\r\n                </div>\r\n                <button\r\n                    onClick={onClose}\r\n                    className=\"flex-shrink-0 ml-3 opacity-70 hover:opacity-100 transition-opacity\"\r\n                >\r\n                    <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                    </svg>\r\n                </button>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Toast;\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAUA,MAAM,QAA8B,CAAC,EACjC,OAAO,EACP,IAAI,EACJ,IAAI,EACJ,OAAO,EACP,WAAW,IAAI,EAClB;IACG,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,MAAM;YACN,MAAM,QAAQ,WAAW;gBACrB;YACJ,GAAG;YACH,OAAO,IAAM,aAAa;QAC9B;IACJ,GAAG;QAAC;QAAM;QAAU;KAAQ;IAE5B,IAAI,CAAC,MAAM,OAAO;IAElB,MAAM,UAAU;QACZ,OAAQ;YACJ,KAAK;gBACD,qBACI,8OAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BAC/D,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAGjF,KAAK;gBACD,qBACI,8OAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BAC/D,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAGjF,KAAK;gBACD,qBACI,8OAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BAC/D,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAGjF;gBACI,qBACI,8OAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BAC/D,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;QAGrF;IACJ;IAEA,MAAM,YAAY;QACd,OAAQ;YACJ,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX;gBACI,OAAO;QACf;IACJ;IAEA,qBACI,8OAAC;QAAI,WAAU;kBACX,cAAA,8OAAC;YAAI,WAAW,CAAC,2DAA2D,EAAE,aAAa;;8BACvF,8OAAC;oBAAI,WAAU;8BACV;;;;;;8BAEL,8OAAC;oBAAI,WAAU;8BACX,cAAA,8OAAC;wBAAE,WAAU;kCAAuB;;;;;;;;;;;8BAExC,8OAAC;oBACG,SAAS;oBACT,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCAC/D,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM7F;uCAEe", "debugId": null}}, {"offset": {"line": 2076, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/patient/AppointmentHistory/AppointmentHistory.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport { useAppointments } from '@/hooks/useAppointments';\r\nimport { useToast } from '@/hooks/useToast';\r\nimport { AppointmentResponse } from '@/types/appointment';\r\nimport { AppointmentCard, AppointmentFilters } from './index';\r\nimport { LoadingSpinner } from '@/components/ui/LoadingSpinner';\r\nimport Toast from '@/components/ui/Toast';\r\nimport { formatDateString } from '@/utils/dateUtils';\r\n\r\ninterface AppointmentHistoryProps {\r\n    className?: string;\r\n}\r\n\r\nconst AppointmentHistory: React.FC<AppointmentHistoryProps> = ({ className = '' }) => {\r\n    const {\r\n        appointments,\r\n        loading,\r\n        error,\r\n        fetchAppointments,\r\n        getUpcomingAppointments,\r\n        getCompletedAppointments,\r\n        getCancelledAppointments,\r\n    } = useAppointments();\r\n\r\n    const { toast, hideToast, showSuccess, showError } = useToast();\r\n\r\n    const [activeFilter, setActiveFilter] = useState<'all' | 'upcoming' | 'completed' | 'cancelled'>('all');\r\n    const [searchTerm, setSearchTerm] = useState('');\r\n\r\n    const getFilteredAppointments = (): AppointmentResponse[] => {\r\n        let filtered = appointments;\r\n\r\n        // Filter by status\r\n        switch (activeFilter) {\r\n            case 'upcoming':\r\n                filtered = getUpcomingAppointments();\r\n                break;\r\n            case 'completed':\r\n                filtered = getCompletedAppointments();\r\n                break;\r\n            case 'cancelled':\r\n                filtered = getCancelledAppointments();\r\n                break;\r\n            default:\r\n                filtered = appointments;\r\n        }\r\n\r\n        // Filter by search term\r\n        if (searchTerm.trim()) {\r\n            filtered = filtered.filter(appointment =>\r\n                appointment.doctor.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n                appointment.appointmentNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n                appointment.reasonForVisit.toLowerCase().includes(searchTerm.toLowerCase())\r\n            );\r\n        }\r\n\r\n        return filtered;\r\n    };\r\n\r\n    const handleRefresh = async () => {\r\n        try {\r\n            await fetchAppointments();\r\n            showSuccess('Đã làm mới danh sách cuộc hẹn');\r\n        } catch (err) {\r\n            showError('Có lỗi xảy ra khi làm mới danh sách');\r\n        }\r\n    };\r\n\r\n    const filteredAppointments = getFilteredAppointments();\r\n\r\n    if (loading && appointments.length === 0) {\r\n        return (\r\n            <div className={`flex justify-center items-center min-h-[400px] ${className}`}>\r\n                <LoadingSpinner size=\"lg\" />\r\n            </div>\r\n        );\r\n    }\r\n\r\n    if (error && appointments.length === 0) {\r\n        return (\r\n            <div className={`text-center py-8 ${className}`}>\r\n                <div className=\"text-red-500 mb-4\">\r\n                    <svg className=\"w-16 h-16 mx-auto mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\" />\r\n                    </svg>\r\n                    <p className=\"text-lg font-semibold\">Có lỗi xảy ra</p>\r\n                    <p className=\"text-gray-600\">{error}</p>\r\n                </div>\r\n                <button\r\n                    onClick={handleRefresh}\r\n                    className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\r\n                >\r\n                    Thử lại\r\n                </button>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    return (\r\n        <>\r\n            <div className={`space-y-6 ${className}`}>\r\n                {/* Header */}\r\n                <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\r\n                    <div>\r\n                        <h1 className=\"text-2xl font-bold text-gray-900\">Lịch sử cuộc hẹn</h1>\r\n                        <p className=\"text-gray-600 mt-1\">\r\n                            Quản lý và theo dõi các cuộc hẹn của bạn\r\n                        </p>\r\n                    </div>\r\n                    <button\r\n                        onClick={handleRefresh}\r\n                        disabled={loading}\r\n                        className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2\"\r\n                    >\r\n                        <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\r\n                        </svg>\r\n                        Làm mới\r\n                    </button>\r\n                </div>\r\n\r\n                {/* Filters */}\r\n                <AppointmentFilters\r\n                    activeFilter={activeFilter}\r\n                    onFilterChange={setActiveFilter}\r\n                    searchTerm={searchTerm}\r\n                    onSearchChange={setSearchTerm}\r\n                    appointmentCount={filteredAppointments.length}\r\n                />\r\n\r\n                {/* Appointments List */}\r\n                <div className=\"space-y-4\">\r\n                    {loading && appointments.length > 0 && (\r\n                        <div className=\"flex justify-center py-4\">\r\n                            <LoadingSpinner size=\"md\" />\r\n                        </div>\r\n                    )}\r\n\r\n                    {filteredAppointments.length === 0 ? (\r\n                        <div className=\"text-center py-12\">\r\n                            <svg className=\"w-16 h-16 mx-auto mb-4 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\r\n                            </svg>\r\n                            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\r\n                                {activeFilter === 'all' ? 'Chưa có cuộc hẹn nào' :\r\n                                 activeFilter === 'upcoming' ? 'Không có cuộc hẹn sắp tới' :\r\n                                 activeFilter === 'completed' ? 'Chưa có cuộc hẹn hoàn thành' :\r\n                                 'Chưa có cuộc hẹn bị hủy'}\r\n                            </h3>\r\n                            <p className=\"text-gray-600\">\r\n                                {activeFilter === 'all' ? 'Bạn chưa có cuộc hẹn nào. Hãy đặt lịch hẹn với bác sĩ ngay!' :\r\n                                 activeFilter === 'upcoming' ? 'Hiện tại không có cuộc hẹn sắp tới.' :\r\n                                 activeFilter === 'completed' ? 'Bạn chưa có cuộc hẹn nào đã hoàn thành.' :\r\n                                 'Bạn chưa có cuộc hẹn nào bị hủy.'}\r\n                            </p>\r\n                        </div>\r\n                    ) : (\r\n                        <div className=\"grid gap-4\">\r\n                            {filteredAppointments.map((appointment) => (\r\n                                <AppointmentCard\r\n                                    key={appointment.appointmentId}\r\n                                    appointment={appointment}\r\n                                    onRefresh={handleRefresh}\r\n                                />\r\n                            ))}\r\n                        </div>\r\n                    )}\r\n                </div>\r\n            </div>\r\n\r\n            {/* Toast Notification */}\r\n            <Toast\r\n                show={toast.show}\r\n                message={toast.message}\r\n                type={toast.type}\r\n                onClose={hideToast}\r\n            />\r\n        </>\r\n    );\r\n};\r\n\r\nexport default AppointmentHistory; "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAAA;AAAA;AACA;AACA;AARA;;;;;;;;AAeA,MAAM,qBAAwD,CAAC,EAAE,YAAY,EAAE,EAAE;IAC7E,MAAM,EACF,YAAY,EACZ,OAAO,EACP,KAAK,EACL,iBAAiB,EACjB,uBAAuB,EACvB,wBAAwB,EACxB,wBAAwB,EAC3B,GAAG,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD;IAElB,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAE5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkD;IACjG,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,0BAA0B;QAC5B,IAAI,WAAW;QAEf,mBAAmB;QACnB,OAAQ;YACJ,KAAK;gBACD,WAAW;gBACX;YACJ,KAAK;gBACD,WAAW;gBACX;YACJ,KAAK;gBACD,WAAW;gBACX;YACJ;gBACI,WAAW;QACnB;QAEA,wBAAwB;QACxB,IAAI,WAAW,IAAI,IAAI;YACnB,WAAW,SAAS,MAAM,CAAC,CAAA,cACvB,YAAY,MAAM,CAAC,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzE,YAAY,iBAAiB,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC3E,YAAY,cAAc,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAEhF;QAEA,OAAO;IACX;IAEA,MAAM,gBAAgB;QAClB,IAAI;YACA,MAAM;YACN,YAAY;QAChB,EAAE,OAAO,KAAK;YACV,UAAU;QACd;IACJ;IAEA,MAAM,uBAAuB;IAE7B,IAAI,WAAW,aAAa,MAAM,KAAK,GAAG;QACtC,qBACI,8OAAC;YAAI,WAAW,CAAC,+CAA+C,EAAE,WAAW;sBACzE,cAAA,8OAAC,0IAAA,CAAA,iBAAc;gBAAC,MAAK;;;;;;;;;;;IAGjC;IAEA,IAAI,SAAS,aAAa,MAAM,KAAK,GAAG;QACpC,qBACI,8OAAC;YAAI,WAAW,CAAC,iBAAiB,EAAE,WAAW;;8BAC3C,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAI,WAAU;4BAAyB,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC9E,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;sCAEzE,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;sCACrC,8OAAC;4BAAE,WAAU;sCAAiB;;;;;;;;;;;;8BAElC,8OAAC;oBACG,SAAS;oBACT,WAAU;8BACb;;;;;;;;;;;;IAKb;IAEA,qBACI;;0BACI,8OAAC;gBAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;kCAEpC,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;;kDACG,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;0CAItC,8OAAC;gCACG,SAAS;gCACT,UAAU;gCACV,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC/D,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;oCACnE;;;;;;;;;;;;;kCAMd,8OAAC,0NAAA,CAAA,qBAAkB;wBACf,cAAc;wBACd,gBAAgB;wBAChB,YAAY;wBACZ,gBAAgB;wBAChB,kBAAkB,qBAAqB,MAAM;;;;;;kCAIjD,8OAAC;wBAAI,WAAU;;4BACV,WAAW,aAAa,MAAM,GAAG,mBAC9B,8OAAC;gCAAI,WAAU;0CACX,cAAA,8OAAC,0IAAA,CAAA,iBAAc;oCAAC,MAAK;;;;;;;;;;;4BAI5B,qBAAqB,MAAM,KAAK,kBAC7B,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAI,WAAU;wCAAuC,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC5F,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;kDAEzE,8OAAC;wCAAG,WAAU;kDACT,iBAAiB,QAAQ,yBACzB,iBAAiB,aAAa,8BAC9B,iBAAiB,cAAc,gCAC/B;;;;;;kDAEL,8OAAC;wCAAE,WAAU;kDACR,iBAAiB,QAAQ,gEACzB,iBAAiB,aAAa,wCAC9B,iBAAiB,cAAc,4CAC/B;;;;;;;;;;;qDAIT,8OAAC;gCAAI,WAAU;0CACV,qBAAqB,GAAG,CAAC,CAAC,4BACvB,8OAAC,oNAAA,CAAA,kBAAe;wCAEZ,aAAa;wCACb,WAAW;uCAFN,YAAY,aAAa;;;;;;;;;;;;;;;;;;;;;;0BAWtD,8OAAC,iIAAA,CAAA,UAAK;gBACF,MAAM,MAAM,IAAI;gBAChB,SAAS,MAAM,OAAO;gBACtB,MAAM,MAAM,IAAI;gBAChB,SAAS;;;;;;;;AAIzB;uCAEe", "debugId": null}}, {"offset": {"line": 2402, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/app/%28page%29/%28patient%29/appointments/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport AppointmentHistory from '@/components/patient/AppointmentHistory/AppointmentHistory';\r\n\r\nconst AppointmentsPage: React.FC = () => {\r\n    return (\r\n        <div className=\"min-h-screen bg-gray-50 py-8\">\r\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n                <AppointmentHistory />\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default AppointmentsPage; "], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,mBAA6B;IAC/B,qBACI,8OAAC;QAAI,WAAU;kBACX,cAAA,8OAAC;YAAI,WAAU;sBACX,cAAA,8OAAC,yKAAA,CAAA,UAAkB;;;;;;;;;;;;;;;AAInC;uCAEe", "debugId": null}}]}