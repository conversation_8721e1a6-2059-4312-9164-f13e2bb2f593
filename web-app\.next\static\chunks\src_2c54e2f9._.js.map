{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/providers/ToastProvider.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Toaster } from 'react-hot-toast';\r\n\r\nexport default function ToastProvider() {\r\n    return (\r\n        <Toaster\r\n            position=\"top-right\"\r\n            toastOptions={{\r\n                duration: 4000,\r\n                style: {\r\n                    background: '#363636',\r\n                    color: '#fff',\r\n                    fontSize: '14px',\r\n                    borderRadius: '8px',\r\n                    padding: '12px 16px',\r\n                    boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',\r\n                },\r\n            }}\r\n        />\r\n    );\r\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACpB,qBACI,6LAAC,0JAAA,CAAA,UAAO;QACJ,UAAS;QACT,cAAc;YACV,UAAU;YACV,OAAO;gBACH,YAAY;gBACZ,OAAO;gBACP,UAAU;gBACV,cAAc;gBACd,SAAS;gBACT,WAAW;YACf;QACJ;;;;;;AAGZ;KAjBwB", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/redux/slice/authSlice.ts"], "sourcesContent": ["import { createSlice } from '@reduxjs/toolkit';\r\n\r\ninterface AuthState {\r\n    isLoggedIn: boolean;\r\n}\r\n\r\nconst initialState: AuthState = {\r\n    isLoggedIn: false,\r\n};\r\n\r\nconst authSlice = createSlice({\r\n    name: 'auth',\r\n    initialState,\r\n    reducers: {\r\n        login: (state) => {\r\n            state.isLoggedIn = true;\r\n        },\r\n        logout: (state) => {\r\n            state.isLoggedIn = false;\r\n        },\r\n    },\r\n});\r\n\r\nexport const { login, logout } = authSlice.actions;\r\nexport default authSlice.reducer;"], "names": [], "mappings": ";;;;;AAAA;;AAMA,MAAM,eAA0B;IAC5B,YAAY;AAChB;AAEA,MAAM,YAAY,CAAA,GAAA,8LAAA,CAAA,cAAW,AAAD,EAAE;IAC1B,MAAM;IACN;IACA,UAAU;QACN,OAAO,CAAC;YACJ,MAAM,UAAU,GAAG;QACvB;QACA,QAAQ,CAAC;YACL,MAAM,UAAU,GAAG;QACvB;IACJ;AACJ;AAEO,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,UAAU,OAAO;uCACnC,UAAU,OAAO", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/redux/store.ts"], "sourcesContent": ["import { configureStore } from '@reduxjs/toolkit'\r\nimport authReducer from './slice/authSlice';\r\n\r\nexport const makeStore = () => {\r\n    return configureStore({\r\n        reducer: {\r\n            auth: authReducer,\r\n        }\r\n    })\r\n}\r\n\r\nexport type AppStore = ReturnType<typeof makeStore>\r\nexport type RootState = ReturnType<AppStore['getState']>\r\nexport type AppDispatch = AppStore['dispatch']"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,MAAM,YAAY;IACrB,OAAO,CAAA,GAAA,8LAAA,CAAA,iBAAc,AAAD,EAAE;QAClB,SAAS;YACL,MAAM,qIAAA,CAAA,UAAW;QACrB;IACJ;AACJ", "debugId": null}}, {"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/redux/StoreProvider.tsx"], "sourcesContent": ["'use client'\r\nimport { useRef } from 'react'\r\nimport { AppStore, makeStore } from './store'\r\nimport { Provider } from 'react-redux'\r\n\r\nexport default function StoreProvider({\r\n    children\r\n}: {\r\n    children: React.ReactNode\r\n}) {\r\n    const storeRef = useRef<AppStore | null>(null)\r\n    if (!storeRef.current) {\r\n        storeRef.current = makeStore()\r\n    }\r\n\r\n    return <Provider store={storeRef.current}>{children}</Provider>\r\n}"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;AAKe,SAAS,cAAc,EAClC,QAAQ,EAGX;;IACG,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAmB;IACzC,IAAI,CAAC,SAAS,OAAO,EAAE;QACnB,SAAS,OAAO,GAAG,CAAA,GAAA,wHAAA,CAAA,YAAS,AAAD;IAC/B;IAEA,qBAAO,6LAAC,4JAAA,CAAA,WAAQ;QAAC,OAAO,SAAS,OAAO;kBAAG;;;;;;AAC/C;GAXwB;KAAA", "debugId": null}}, {"offset": {"line": 144, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/layouts/Header.tsx"], "sourcesContent": ["'use client'\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { Heart, Search, ShoppingBag, UserCircle, LogIn, UserPlus, History, LogOut, User, Calendar, Settings, Bell } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useAppSelector, useAppDispatch } from '@/redux/hook';\r\nimport { logout } from '@/redux/slice/authSlice';\r\n\r\nconst Header = () => {\r\n    const [token, setToken] = useState<string | null>(null);\r\n\r\n    useEffect(() => {\r\n        setToken(localStorage.getItem('accessToken'));\r\n    }, []);\r\n\r\n    const [isDropdownOpen, setIsDropdownOpen] = useState(false);\r\n    const router = useRouter();\r\n\r\n    // Đóng dropdown khi click outside\r\n    useEffect(() => {\r\n        const handleClickOutside = (event: MouseEvent) => {\r\n            const target = event.target as Element;\r\n            if (!target.closest('.user-dropdown')) {\r\n                setIsDropdownOpen(false);\r\n            }\r\n        };\r\n\r\n        if (isDropdownOpen) {\r\n            document.addEventListener('mousedown', handleClickOutside);\r\n        }\r\n\r\n        return () => {\r\n            document.removeEventListener('mousedown', handleClickOutside);\r\n        };\r\n    }, [isDropdownOpen]);\r\n\r\n    const handleLogout = () => {\r\n        localStorage.removeItem(\"accessToken\");\r\n        localStorage.removeItem(\"refreshToken\");\r\n        setToken(null);\r\n        setIsDropdownOpen(false);\r\n        router.push(\"/login\");\r\n    };\r\n\r\n    return (\r\n        <div>\r\n            <header className=\"bg-white shadow-sm sticky top-0 z-50\">\r\n                <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n                    <div className=\"flex justify-between items-center h-16\">\r\n                        <div className=\"flex items-center space-x-2\">\r\n                            <div className=\"w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center\">\r\n                                <Heart className=\"w-6 h-6 text-white\" />\r\n                            </div>\r\n                            <span className=\"text-2xl font-bold text-gray-900\">Medically</span>\r\n                        </div>\r\n\r\n                        <nav className=\"hidden md:flex space-x-8\">\r\n                            <Link href=\"/\" className=\"text-blue-600 font-medium hover:text-blue-700 transition-colors\">Home</Link>\r\n                            <Link href=\"/about\" className=\"text-gray-600 hover:text-gray-900 transition-colors\">About</Link>\r\n                            <Link href=\"/doctor\" className=\"text-gray-600 hover:text-gray-900 transition-colors\">Doctors</Link>\r\n                            <Link href=\"/services\" className=\"text-gray-600 hover:text-gray-900 transition-colors\">Services</Link>\r\n                            <Link href=\"/portfolio\" className=\"text-gray-600 hover:text-gray-900 transition-colors\">Portfolio</Link>\r\n                            <Link href=\"/blog\" className=\"text-gray-600 hover:text-gray-900 transition-colors\">Blog</Link>\r\n                            <Link href=\"/contact\" className=\"text-gray-600 hover:text-gray-900 transition-colors\">Contact</Link>\r\n                        </nav>\r\n\r\n                        {/* Right side icons */}\r\n                        <div className=\"flex items-center space-x-6\">\r\n                            <Search className=\"w-6 h-6 text-gray-600 hover:text-gray-900 cursor-pointer transition-colors\" />\r\n                            <div className=\"relative\">\r\n                                <ShoppingBag className=\"w-6 h-6 text-gray-600 hover:text-gray-900 cursor-pointer transition-colors\" />\r\n                                <span className=\"absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\">0</span>\r\n                            </div>\r\n\r\n                            {/* User Dropdown */}\r\n                            <div className=\"relative user-dropdown\">\r\n                                <button\r\n                                    onClick={() => setIsDropdownOpen(!isDropdownOpen)}\r\n                                    className=\"flex items-center space-x-2 focus:outline-none bg-blue-50 hover:bg-blue-100 p-2 rounded-full transition-colors\"\r\n                                >\r\n                                    <UserCircle className=\"w-7 h-7 text-blue-600\" />\r\n                                </button>\r\n\r\n                                {/* Dropdown Menu */}\r\n                                {isDropdownOpen && (\r\n                                    <div className=\"absolute right-0 mt-3 w-64 bg-white rounded-lg shadow-xl py-2 z-50 border border-gray-200\">\r\n                                        <div className=\"px-4 py-3 border-b border-gray-200\">\r\n                                            {token ? (\r\n                                                <>\r\n                                                    <p className=\"text-sm text-gray-500\">Xin chào</p>\r\n                                                    <p className=\"font-medium text-gray-900\">Bệnh nhân</p>\r\n                                                </>\r\n                                            ) : (\r\n                                                <>\r\n                                                    <p className=\"text-sm text-gray-500\">Welcome to</p>\r\n                                                    <p className=\"font-medium text-gray-900\">Medically Healthcare</p>\r\n                                                </>\r\n                                            )}\r\n                                        </div>\r\n\r\n                                        {!token &&\r\n                                            <>\r\n                                                <Link href=\"/login\" className=\"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 transition-colors\">\r\n                                                    <LogIn className=\"w-5 h-5 mr-3 text-blue-600\" />\r\n                                                    <span className=\"text-base\">Login</span>\r\n                                                </Link>\r\n\r\n                                                <Link href=\"/registration\" className=\"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 transition-colors\">\r\n                                                    <UserPlus className=\"w-5 h-5 mr-3 text-blue-600\" />\r\n                                                    <span className=\"text-base\">Register</span>\r\n                                                </Link>\r\n                                            </>\r\n                                        }\r\n\r\n\r\n                                        {token && (\r\n                                            <>\r\n                                                <Link href=\"/profile\" className=\"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 transition-colors\">\r\n                                                    <User className=\"w-5 h-5 mr-3 text-blue-600\" />\r\n                                                    <span className=\"text-base\">Hồ sơ cá nhân</span>\r\n                                                </Link>\r\n\r\n                                                <Link href=\"/appointments\" className=\"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 transition-colors\">\r\n                                                    <Calendar className=\"w-5 h-5 mr-3 text-blue-600\" />\r\n                                                    <span className=\"text-base\">Lịch hẹn</span>\r\n                                                </Link>\r\n\r\n                                                <Link href=\"/notifications\" className=\"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 transition-colors\">\r\n                                                    <Bell className=\"w-5 h-5 mr-3 text-blue-600\" />\r\n                                                    <span className=\"text-base\">Thông báo</span>\r\n                                                </Link>\r\n\r\n                                                <Link href=\"/settings\" className=\"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 transition-colors\">\r\n                                                    <Settings className=\"w-5 h-5 mr-3 text-blue-600\" />\r\n                                                    <span className=\"text-base\">Cài đặt</span>\r\n                                                </Link>\r\n\r\n                                                <Link href=\"/appointments\" className=\"flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 transition-colors\">\r\n                                                    <History className=\"w-5 h-5 mr-3 text-blue-600\" />\r\n                                                    <span className=\"text-base\">Lịch sử đặt hẹn</span>\r\n                                                </Link>\r\n\r\n                                                <div className=\"border-t border-gray-200 my-2\"></div>\r\n                                            </>\r\n                                        )}\r\n\r\n\r\n                                        {\r\n                                            token &&\r\n                                            <button\r\n                                                onClick={handleLogout}\r\n                                                className=\"flex items-center px-4 py-3 text-red-600 hover:bg-red-50 transition-colors w-full text-left\"\r\n                                            >\r\n                                                <LogOut className=\"w-5 h-5 mr-3\" />\r\n                                                <span className=\"text-base font-medium\">Logout</span>\r\n                                            </button>\r\n                                        }\r\n                                    </div>\r\n                                )}\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </header>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Header;"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AAJA;;;;;AAQA,MAAM,SAAS;;IACX,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACN,SAAS,aAAa,OAAO,CAAC;QAClC;2BAAG,EAAE;IAEL,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACN,MAAM;uDAAqB,CAAC;oBACxB,MAAM,SAAS,MAAM,MAAM;oBAC3B,IAAI,CAAC,OAAO,OAAO,CAAC,mBAAmB;wBACnC,kBAAkB;oBACtB;gBACJ;;YAEA,IAAI,gBAAgB;gBAChB,SAAS,gBAAgB,CAAC,aAAa;YAC3C;YAEA;oCAAO;oBACH,SAAS,mBAAmB,CAAC,aAAa;gBAC9C;;QACJ;2BAAG;QAAC;KAAe;IAEnB,MAAM,eAAe;QACjB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,SAAS;QACT,kBAAkB;QAClB,OAAO,IAAI,CAAC;IAChB;IAEA,qBACI,6LAAC;kBACG,cAAA,6LAAC;YAAO,WAAU;sBACd,cAAA,6LAAC;gBAAI,WAAU;0BACX,cAAA,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAI,WAAU;8CACX,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAErB,6LAAC;oCAAK,WAAU;8CAAmC;;;;;;;;;;;;sCAGvD,6LAAC;4BAAI,WAAU;;8CACX,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAkE;;;;;;8CAC3F,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAsD;;;;;;8CACpF,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAU,WAAU;8CAAsD;;;;;;8CACrF,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAU;8CAAsD;;;;;;8CACvF,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;8CAAsD;;;;;;8CACxF,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;8CAAsD;;;;;;8CACnF,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAsD;;;;;;;;;;;;sCAI1F,6LAAC;4BAAI,WAAU;;8CACX,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;oCAAI,WAAU;;sDACX,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,6LAAC;4CAAK,WAAU;sDAA+G;;;;;;;;;;;;8CAInI,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CACG,SAAS,IAAM,kBAAkB,CAAC;4CAClC,WAAU;sDAEV,cAAA,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;wCAIzB,gCACG,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;oDAAI,WAAU;8DACV,sBACG;;0EACI,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC;gEAAE,WAAU;0EAA4B;;;;;;;qFAG7C;;0EACI,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC;gEAAE,WAAU;0EAA4B;;;;;;;;;;;;;gDAKpD,CAAC,uBACE;;sEACI,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAS,WAAU;;8EAC1B,6LAAC,2MAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,6LAAC;oEAAK,WAAU;8EAAY;;;;;;;;;;;;sEAGhC,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAgB,WAAU;;8EACjC,6LAAC,iNAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,6LAAC;oEAAK,WAAU;8EAAY;;;;;;;;;;;;;;gDAMvC,uBACG;;sEACI,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAW,WAAU;;8EAC5B,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC;oEAAK,WAAU;8EAAY;;;;;;;;;;;;sEAGhC,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAgB,WAAU;;8EACjC,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,6LAAC;oEAAK,WAAU;8EAAY;;;;;;;;;;;;sEAGhC,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAiB,WAAU;;8EAClC,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC;oEAAK,WAAU;8EAAY;;;;;;;;;;;;sEAGhC,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAY,WAAU;;8EAC7B,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,6LAAC;oEAAK,WAAU;8EAAY;;;;;;;;;;;;sEAGhC,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAgB,WAAU;;8EACjC,6LAAC,2MAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;8EACnB,6LAAC;oEAAK,WAAU;8EAAY;;;;;;;;;;;;sEAGhC,6LAAC;4DAAI,WAAU;;;;;;;;gDAMnB,uBACA,6LAAC;oDACG,SAAS;oDACT,WAAU;;sEAEV,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6LAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYxF;GA9JM;;QAQa,qIAAA,CAAA,YAAS;;;KARtB;uCAgKS", "debugId": null}}, {"offset": {"line": 695, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/layouts/Footer.tsx"], "sourcesContent": ["import { Heart } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\n\r\nconst Footer = () => {\r\n    return (\r\n        <footer className=\"bg-gray-900 text-white py-12\">\r\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n                <div className=\"grid md:grid-cols-4 gap-8\">\r\n                    <div>\r\n                        <div className=\"flex items-center space-x-2 mb-4\">\r\n                            <div className=\"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center\">\r\n                                <Heart className=\"w-5 h-5 text-white\" />\r\n                            </div>\r\n                            <span className=\"text-xl font-bold\">Medically</span>\r\n                        </div>\r\n                        <p className=\"text-gray-400\">\r\n                            Providing quality healthcare services to help you live a healthy and happy life.\r\n                        </p>\r\n                    </div>\r\n\r\n                    <div>\r\n                        <h3 className=\"font-semibold mb-4\">Quick Links</h3>\r\n                        <ul className=\"space-y-2 text-gray-400\">\r\n                            <li><Link href=\"#\" className=\"hover:text-white transition-colors\">About Us</Link></li>\r\n                            <li><Link href=\"#\" className=\"hover:text-white transition-colors\">Services</Link></li>\r\n                            <li><Link href=\"#\" className=\"hover:text-white transition-colors\">Doctors</Link></li>\r\n                            <li><Link href=\"#\" className=\"hover:text-white transition-colors\">Contact</Link></li>\r\n                        </ul>\r\n                    </div>\r\n\r\n                    <div>\r\n                        <h3 className=\"font-semibold mb-4\">Services</h3>\r\n                        <ul className=\"space-y-2 text-gray-400\">\r\n                            <li><Link href=\"#\" className=\"hover:text-white transition-colors\">Cardiology</Link></li>\r\n                            <li><Link href=\"#\" className=\"hover:text-white transition-colors\">Family Medicine</Link></li>\r\n                            <li><Link href=\"#\" className=\"hover:text-white transition-colors\">Emergency Care</Link></li>\r\n                            <li><Link href=\"#\" className=\"hover:text-white transition-colors\">Pediatrics</Link></li>\r\n                        </ul>\r\n                    </div>\r\n\r\n                    <div>\r\n                        <h3 className=\"font-semibold mb-4\">Contact Info</h3>\r\n                        <div className=\"space-y-2 text-gray-400\">\r\n                            <p>123 Medical Center Drive</p>\r\n                            <p>Healthcare City, HC 12345</p>\r\n                            <p>Phone: (*************</p>\r\n                            <p>Email: <EMAIL></p>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div className=\"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400\">\r\n                    <p>&copy; 2025 Medically. All rights reserved.</p>\r\n                </div>\r\n            </div>\r\n        </footer>\r\n    )\r\n}\r\n\r\nexport default Footer;"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,SAAS;IACX,qBACI,6LAAC;QAAO,WAAU;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BACX,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;;8CACG,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAI,WAAU;sDACX,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAErB,6LAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;8CAExC,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAKjC,6LAAC;;8CACG,6LAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,6LAAC;oCAAG,WAAU;;sDACV,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAqC;;;;;;;;;;;sDAClE,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAqC;;;;;;;;;;;sDAClE,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAqC;;;;;;;;;;;sDAClE,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;sCAI1E,6LAAC;;8CACG,6LAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,6LAAC;oCAAG,WAAU;;sDACV,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAqC;;;;;;;;;;;sDAClE,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAqC;;;;;;;;;;;sDAClE,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAqC;;;;;;;;;;;sDAClE,6LAAC;sDAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;sCAI1E,6LAAC;;8CACG,6LAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;sDAAE;;;;;;sDACH,6LAAC;sDAAE;;;;;;sDACH,6LAAC;sDAAE;;;;;;sDACH,6LAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;8BAKf,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAKvB;KAtDM;uCAwDS", "debugId": null}}, {"offset": {"line": 1028, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/layouts/LayoutWrapper.tsx"], "sourcesContent": ["'use client'\r\nimport { usePathname } from \"next/navigation\";\r\nimport Header from \"@/components/layouts/Header\";\r\nimport Footer from \"@/components/layouts/Footer\";\r\n\r\nexport default function LayoutWrapper({\r\n    children\r\n}: {\r\n    children: React.ReactNode\r\n}) {\r\n    const pathname = usePathname();\r\n\r\n    const noLayoutPages = ['/login', '/register', '/registration', \"/receptionist\", \"/forgot-password\"];\r\n    const shouldShowLayout = !noLayoutPages.includes(pathname);\r\n\r\n    return (\r\n        <>\r\n            {shouldShowLayout && <Header />}\r\n            {children}\r\n            {shouldShowLayout && <Footer />}\r\n        </>\r\n    );\r\n}"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;AAKe,SAAS,cAAc,EAClC,QAAQ,EAGX;;IACG,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,gBAAgB;QAAC;QAAU;QAAa;QAAiB;QAAiB;KAAmB;IACnG,MAAM,mBAAmB,CAAC,cAAc,QAAQ,CAAC;IAEjD,qBACI;;YACK,kCAAoB,6LAAC,0IAAA,CAAA,UAAM;;;;;YAC3B;YACA,kCAAoB,6LAAC,0IAAA,CAAA,UAAM;;;;;;;AAGxC;GAjBwB;;QAKH,qIAAA,CAAA,cAAW;;;KALR", "debugId": null}}]}