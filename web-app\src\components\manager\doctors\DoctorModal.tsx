'use client';

import { useState } from 'react';
import { X, User, Mail, Phone, GraduationCap, FileText, DollarSign, Calendar, Stethoscope, Award } from 'lucide-react';
import { DoctorCreationRequest, DoctorUpdateRequest, Gender } from '@/types/doctor';
import { DoctorDetailResponse } from '@/types/doctor';
import { SpecialtyDetailResponse } from '@/types/specialty';

interface DoctorModalProps {
    isOpen: boolean;
    doctor: DoctorDetailResponse | null;
    specialties: SpecialtyDetailResponse[];
    onClose: () => void;
    onSubmit: (data: DoctorCreationRequest | DoctorUpdateRequest) => void;
}

// Input Field Component
const InputField = ({
    label,
    name,
    type = 'text',
    defaultValue,
    required = false,
    icon: Icon,
    error,
    placeholder,
    ...props
}: {
    label: string;
    name: string;
    type?: string;
    defaultValue?: string | number;
    required?: boolean;
    icon?: any;
    error?: string;
    placeholder?: string;
    [key: string]: any;
}) => (
    <div className="space-y-2">
        <label className="block text-sm font-semibold text-gray-700 flex items-center space-x-2">
            {Icon && <Icon className="w-4 h-4 text-gray-500" />}
            <span>{label} {required && <span className="text-red-500">*</span>}</span>
        </label>
        <div className="relative">
            <input
                name={name}
                type={type}
                defaultValue={defaultValue}
                required={required}
                placeholder={placeholder}
                className={`w-full px-4 py-3 border-2 rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 ${error
                    ? 'border-red-300 bg-red-50 focus:border-red-500 focus:ring-red-500/20'
                    : 'border-gray-200 bg-gray-50 hover:border-gray-300 focus:bg-white'
                    } ${Icon ? 'pl-11' : ''}`}
                {...props}
            />
            {Icon && (
                <Icon className={`absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 ${error ? 'text-red-400' : 'text-gray-400'
                    }`} />
            )}
        </div>
        {error && (
            <p className="text-sm text-red-600 flex items-center space-x-1">
                <span>⚠</span>
                <span>{error}</span>
            </p>
        )}
    </div>
);

// Select Field Component
const SelectField = ({
    label,
    name,
    defaultValue,
    required = false,
    icon: Icon,
    error,
    children,
    ...props
}: {
    label: string;
    name: string;
    defaultValue?: string;
    required?: boolean;
    icon?: any;
    error?: string;
    children: React.ReactNode;
    [key: string]: any;
}) => (
    <div className="space-y-2">
        <label className="block text-sm font-semibold text-gray-700 flex items-center space-x-2">
            {Icon && <Icon className="w-4 h-4 text-gray-500" />}
            <span>{label} {required && <span className="text-red-500">*</span>}</span>
        </label>
        <div className="relative">
            <select
                name={name}
                defaultValue={defaultValue}
                required={required}
                className={`w-full px-4 py-3 border-2 rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 appearance-none bg-white ${error
                    ? 'border-red-300 bg-red-50 focus:border-red-500 focus:ring-red-500/20'
                    : 'border-gray-200 bg-gray-50 hover:border-gray-300 focus:bg-white'
                    } ${Icon ? 'pl-11' : ''}`}
                {...props}
            >
                {children}
            </select>
            {Icon && (
                <Icon className={`absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 ${error ? 'text-red-400' : 'text-gray-400'
                    }`} />
            )}
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
            </div>
        </div>
        {error && (
            <p className="text-sm text-red-600 flex items-center space-x-1">
                <span>⚠</span>
                <span>{error}</span>
            </p>
        )}
    </div>
);

export const DoctorModal = ({ isOpen, doctor, specialties, onClose, onSubmit }: DoctorModalProps) => {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [errors, setErrors] = useState<Record<string, string>>({});

    if (!isOpen) return null;

    const validateForm = (formData: FormData): Record<string, string> => {
        const errors: Record<string, string> = {};

        if (!formData.get('firstName')?.toString().trim()) {
            errors.firstName = 'Tên là bắt buộc';
        }
        if (!formData.get('lastName')?.toString().trim()) {
            errors.lastName = 'Họ là bắt buộc';
        }
        if (!formData.get('email')?.toString().trim()) {
            errors.email = 'Email là bắt buộc';
        }
        if (!formData.get('phone')?.toString().trim()) {
            errors.phone = 'Số điện thoại là bắt buộc';
        }
        if (!formData.get('licenseNumber')?.toString().trim()) {
            errors.licenseNumber = 'Số giấy phép là bắt buộc';
        }

        const consultationFee = parseFloat(formData.get('consultationFee') as string);
        if (isNaN(consultationFee) || consultationFee < 0) {
            errors.consultationFee = 'Phí tư vấn phải là số dương';
        }

        const yearsOfExperience = parseInt(formData.get('yearsOfExperience') as string);
        if (isNaN(yearsOfExperience) || yearsOfExperience < 0) {
            errors.yearsOfExperience = 'Kinh nghiệm phải là số dương';
        }

        return errors;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);
        setErrors({});

        const formData = new FormData(e.target as HTMLFormElement);
        const validationErrors = validateForm(formData);

        if (Object.keys(validationErrors).length > 0) {
            setErrors(validationErrors);
            setIsSubmitting(false);
            return;
        }

        try {
            const baseData = {
                email: formData.get('email') as string,
                phone: formData.get('phone') as string,
                firstName: formData.get('firstName') as string,
                lastName: formData.get('lastName') as string,
                specialtyId: parseInt(formData.get('specialtyId') as string),
                licenseNumber: formData.get('licenseNumber') as string,
                degree: formData.get('degree') as string,
                consultationFee: parseFloat(formData.get('consultationFee') as string),
                gender: formData.get('gender') as Gender,
                yearsOfExperience: parseInt(formData.get('yearsOfExperience') as string),
                bio: formData.get('bio') as string,
                avatar: '',
            };

            if (doctor) {
                const updateData: DoctorUpdateRequest = {
                    ...baseData,
                    id: doctor.doctorId,
                    isAvailable: formData.get('isAvailable') === 'true',
                };
                await onSubmit(updateData);
            } else {
                const createData: DoctorCreationRequest = {
                    ...baseData,
                };
                await onSubmit(createData);
            }
        } catch (error) {
            console.error('Error submitting form:', error);
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[95vh] overflow-hidden animate-in fade-in-0 zoom-in-95 duration-300">
                {/* Header */}
                <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-8 py-6 text-white relative">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                            <div className="p-2 bg-white/20 rounded-lg">
                                <Stethoscope className="w-6 h-6" />
                            </div>
                            <div>
                                <h3 className="text-2xl font-bold">
                                    {doctor ? 'Chỉnh sửa Bác sĩ' : 'Thêm Bác sĩ mới'}
                                </h3>
                                <p className="text-blue-100 text-sm">
                                    {doctor ? 'Cập nhật thông tin bác sĩ' : 'Điền thông tin để thêm bác sĩ mới'}
                                </p>
                            </div>
                        </div>
                        <button
                            onClick={onClose}
                            className="p-2 hover:bg-white/20 rounded-lg transition-colors duration-200"
                            disabled={isSubmitting}
                        >
                            <X className="w-6 h-6" />
                        </button>
                    </div>
                </div>

                {/* Content */}
                <div className="p-8 overflow-y-auto max-h-[calc(95vh-140px)]">
                    <form onSubmit={handleSubmit} className="space-y-8">
                        {/* Personal Information Section */}
                        <div className="bg-gray-50 rounded-xl p-6">
                            <h4 className="text-lg font-semibold text-gray-800 mb-6 flex items-center space-x-2">
                                <User className="w-5 h-5 text-blue-600" />
                                <span>Thông tin cá nhân</span>
                            </h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <InputField
                                    label="Họ"
                                    name="lastName"
                                    defaultValue={doctor?.fullName?.split(' ').slice(0, -1).join(' ') || ''}
                                    required
                                    icon={User}
                                    error={errors.lastName}
                                    placeholder="Nhập họ của bác sĩ"
                                />
                                <InputField
                                    label="Tên"
                                    name="firstName"
                                    defaultValue={doctor?.fullName?.split(' ').slice(-1)[0] || ''}
                                    required
                                    icon={User}
                                    error={errors.firstName}
                                    placeholder="Nhập tên của bác sĩ"
                                />
                                <InputField
                                    label="Email"
                                    name="email"
                                    type="email"
                                    defaultValue={doctor?.email || ''}
                                    required
                                    icon={Mail}
                                    error={errors.email}
                                    placeholder="<EMAIL>"
                                />
                                <InputField
                                    label="Số điện thoại"
                                    name="phone"
                                    type="tel"
                                    defaultValue={doctor?.phone || ''}
                                    required
                                    icon={Phone}
                                    error={errors.phone}
                                    placeholder="0123456789"
                                />
                                <SelectField
                                    label="Giới tính"
                                    name="gender"
                                    defaultValue={doctor?.gender?.toString() || 'MALE'}
                                    icon={User}
                                    error={errors.gender}
                                >
                                    <option value="MALE">Nam</option>
                                    <option value="FEMALE">Nữ</option>
                                    <option value="OTHER">Khác</option>
                                </SelectField>
                            </div>
                        </div>

                        {/* Professional Information Section */}
                        <div className="bg-blue-50 rounded-xl p-6">
                            <h4 className="text-lg font-semibold text-gray-800 mb-6 flex items-center space-x-2">
                                <Stethoscope className="w-5 h-5 text-blue-600" />
                                <span>Thông tin chuyên môn</span>
                            </h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <SelectField
                                    label="Chuyên khoa"
                                    name="specialtyId"
                                    defaultValue={doctor?.specialty.specialtyId?.toString()}
                                    required
                                    icon={Stethoscope}
                                    error={errors.specialtyId}
                                >
                                    <option value="">Chọn chuyên khoa</option>
                                    {specialties.map((s) => (
                                        <option key={s.id} value={s.id}>
                                            {s.specialtyName}
                                        </option>
                                    ))}
                                </SelectField>
                                <InputField
                                    label="Bằng cấp"
                                    name="degree"
                                    defaultValue={doctor?.degree || ''}
                                    icon={GraduationCap}
                                    error={errors.degree}
                                    placeholder="Tiến sĩ, Thạc sĩ, Bác sĩ..."
                                />
                                <InputField
                                    label="Số giấy phép hành nghề"
                                    name="licenseNumber"
                                    defaultValue={doctor?.licenseNumber || ''}
                                    required
                                    icon={Award}
                                    error={errors.licenseNumber}
                                    placeholder="Nhập số giấy phép"
                                />
                                <InputField
                                    label="Kinh nghiệm (năm)"
                                    name="yearsOfExperience"
                                    type="number"
                                    defaultValue={doctor?.yearsOfExperience || 0}
                                    required
                                    icon={Calendar}
                                    error={errors.yearsOfExperience}
                                    placeholder="0"
                                    min="0"
                                />
                                <InputField
                                    label="Phí tư vấn (VNĐ)"
                                    name="consultationFee"
                                    type="number"
                                    defaultValue={doctor?.consultationFee || 0}
                                    required
                                    icon={DollarSign}
                                    error={errors.consultationFee}
                                    placeholder="500000"
                                    min="0"
                                />

                                {/* Chỉ hiển thị khi cập nhật */}
                                {doctor && (
                                    <SelectField
                                        label="Trạng thái hoạt động"
                                        name="isAvailable"
                                        defaultValue={doctor.isAvailable ? 'true' : 'false'}
                                        icon={User}
                                        error={errors.isAvailable}
                                    >
                                        <option value="true">Đang hoạt động</option>
                                        <option value="false">Tạm nghỉ</option>
                                    </SelectField>
                                )}
                            </div>
                        </div>

                        {/* Additional Information Section */}
                        <div className="bg-green-50 rounded-xl p-6">
                            <h4 className="text-lg font-semibold text-gray-800 mb-6 flex items-center space-x-2">
                                <FileText className="w-5 h-5 text-blue-600" />
                                <span>Thông tin bổ sung</span>
                            </h4>
                            <div className="space-y-4">
                                <div className="space-y-2">
                                    <label className="block text-sm font-semibold text-gray-700 flex items-center space-x-2">
                                        <FileText className="w-4 h-4 text-gray-500" />
                                        <span>Tiểu sử và kinh nghiệm</span>
                                    </label>
                                    <textarea
                                        name="bio"
                                        defaultValue={doctor?.bio || ''}
                                        rows={4}
                                        placeholder="Mô tả về kinh nghiệm, chuyên môn và thành tích của bác sĩ..."
                                        className={`w-full px-4 py-3 border-2 rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 resize-none ${errors.bio
                                                ? 'border-red-300 bg-red-50 focus:border-red-500 focus:ring-red-500/20'
                                                : 'border-gray-200 bg-gray-50 hover:border-gray-300 focus:bg-white'
                                            }`}
                                    />
                                    {errors.bio && (
                                        <p className="text-sm text-red-600 flex items-center space-x-1">
                                            <span>⚠</span>
                                            <span>{errors.bio}</span>
                                        </p>
                                    )}
                                </div>
                            </div>
                        </div>

                        {/* Footer Actions */}
                        <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                            <button
                                type="button"
                                onClick={onClose}
                                disabled={isSubmitting}
                                className="px-6 py-3 text-sm font-semibold text-gray-700 bg-white border-2 border-gray-300 rounded-xl hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                Hủy bỏ
                            </button>
                            <button
                                type="submit"
                                disabled={isSubmitting}
                                className="px-8 py-3 text-sm font-semibold text-white bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 shadow-lg hover:shadow-xl"
                            >
                                {isSubmitting ? (
                                    <>
                                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                        <span>Đang xử lý...</span>
                                    </>
                                ) : (
                                    <>
                                        <span>{doctor ? 'Cập nhật' : 'Thêm mới'}</span>
                                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                        </svg>
                                    </>
                                )}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};
