{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/types/user.ts"], "sourcesContent": ["export interface UserCreationRequest {\r\n    email: string;\r\n    phone: string;\r\n    password: string;\r\n    firstName: string;\r\n    lastName: string;\r\n    dob: string;\r\n}\r\n\r\nexport interface UserCreationResponse {\r\n    email: string;\r\n    phone: string;\r\n    firstName: string;\r\n    lastName: string;\r\n    dob: string;\r\n    userType: string;\r\n}\r\n\r\nexport interface User {\r\n    id: string;\r\n    email: string;\r\n    firstName: string;\r\n    lastName: string;\r\n    role: string;\r\n    avatar?: string;\r\n}\r\n\r\nexport enum Gender {\r\n    Male = \"Male\",\r\n    Female = \"Female\",\r\n    Other = \"Other\",\r\n}\r\n\r\nexport interface PatientDetailResponse {\r\n    firstName?: string;\r\n    lastName?: string;\r\n    phone?: string;\r\n    email?: string;\r\n    avatar?: string;\r\n    dob: string; // ISO date string (DateOnly from backend)\r\n    gender?: Gender;\r\n    address?: string;\r\n    enable2FA?: boolean;\r\n}\r\n\r\nexport interface PatientDetailRequest {\r\n    firstName?: string;\r\n    lastName?: string;\r\n    phone?: string;\r\n    email?: string;\r\n    dob: string; // ISO date string (DateOnly from backend)\r\n    gender?: Gender;\r\n    address?: string;\r\n}"], "names": [], "mappings": ";;;AA2BO,IAAA,AAAK,gCAAA;;;;WAAA", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/ui/Toast.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\r\n\r\ninterface ToastProps {\r\n    message: string;\r\n    type: 'success' | 'error' | 'info';\r\n    show: boolean;\r\n    onClose: () => void;\r\n    duration?: number;\r\n}\r\n\r\nconst Toast: React.FC<ToastProps> = ({ \r\n    message, \r\n    type, \r\n    show, \r\n    onClose, \r\n    duration = 3000 \r\n}) => {\r\n    useEffect(() => {\r\n        if (show) {\r\n            const timer = setTimeout(() => {\r\n                onClose();\r\n            }, duration);\r\n            return () => clearTimeout(timer);\r\n        }\r\n    }, [show, duration, onClose]);\r\n\r\n    if (!show) return null;\r\n\r\n    const getIcon = () => {\r\n        switch (type) {\r\n            case 'success':\r\n                return (\r\n                    <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n                    </svg>\r\n                );\r\n            case 'error':\r\n                return (\r\n                    <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\" />\r\n                    </svg>\r\n                );\r\n            case 'info':\r\n                return (\r\n                    <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                    </svg>\r\n                );\r\n            default:\r\n                return (\r\n                    <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                    </svg>\r\n                );\r\n        }\r\n    };\r\n\r\n    const getStyles = () => {\r\n        switch (type) {\r\n            case 'success':\r\n                return 'bg-green-50 border-green-200 text-green-800';\r\n            case 'error':\r\n                return 'bg-red-50 border-red-200 text-red-800';\r\n            case 'info':\r\n                return 'bg-blue-50 border-blue-200 text-blue-800';\r\n            default:\r\n                return 'bg-gray-50 border-gray-200 text-gray-800';\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div className=\"fixed top-4 right-4 z-50 animate-in slide-in-from-top-2 duration-300\">\r\n            <div className={`flex items-center p-4 border rounded-lg shadow-lg max-w-md ${getStyles()}`}>\r\n                <div className=\"flex-shrink-0 mr-3\">\r\n                    {getIcon()}\r\n                </div>\r\n                <div className=\"flex-1\">\r\n                    <p className=\"text-sm font-medium\">{message}</p>\r\n                </div>\r\n                <button\r\n                    onClick={onClose}\r\n                    className=\"flex-shrink-0 ml-3 opacity-70 hover:opacity-100 transition-opacity\"\r\n                >\r\n                    <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                    </svg>\r\n                </button>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Toast;\r\n"], "names": [], "mappings": ";;;;AAAA;;;;AAUA,MAAM,QAA8B,CAAC,EACjC,OAAO,EACP,IAAI,EACJ,IAAI,EACJ,OAAO,EACP,WAAW,IAAI,EAClB;;IACG,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACN,IAAI,MAAM;gBACN,MAAM,QAAQ;6CAAW;wBACrB;oBACJ;4CAAG;gBACH;uCAAO,IAAM,aAAa;;YAC9B;QACJ;0BAAG;QAAC;QAAM;QAAU;KAAQ;IAE5B,IAAI,CAAC,MAAM,OAAO;IAElB,MAAM,UAAU;QACZ,OAAQ;YACJ,KAAK;gBACD,qBACI,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BAC/D,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAGjF,KAAK;gBACD,qBACI,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BAC/D,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAGjF,KAAK;gBACD,qBACI,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BAC/D,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAGjF;gBACI,qBACI,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BAC/D,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;QAGrF;IACJ;IAEA,MAAM,YAAY;QACd,OAAQ;YACJ,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX;gBACI,OAAO;QACf;IACJ;IAEA,qBACI,6LAAC;QAAI,WAAU;kBACX,cAAA,6LAAC;YAAI,WAAW,CAAC,2DAA2D,EAAE,aAAa;;8BACvF,6LAAC;oBAAI,WAAU;8BACV;;;;;;8BAEL,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;wBAAE,WAAU;kCAAuB;;;;;;;;;;;8BAExC,6LAAC;oBACG,SAAS;oBACT,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCAC/D,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM7F;GAhFM;KAAA;uCAkFS", "debugId": null}}, {"offset": {"line": 236, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/utils/baseUrl.ts"], "sourcesContent": ["export const API_URL = process.env.NEXT_PUBLIC_API_URL || 'https://localhost:7166';\r\n"], "names": [], "mappings": ";;;AAAuB;AAAhB,MAAM,UAAU,8DAAmC", "debugId": null}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/services/patientService.ts"], "sourcesContent": ["import { API_URL } from '@/utils/baseUrl';\r\nimport { ApiResponse } from '@/types/apiResonse';\r\nimport { PatientDetailResponse, PatientDetailRequest } from '@/types/user';\r\n\r\nclass PatientService {\r\n    private baseUrl = `${API_URL}/api/v1/patients`;\r\n\r\n    async getPatientProfile(): Promise<ApiResponse<PatientDetailResponse>> {\r\n        try {\r\n            const token = localStorage.getItem('accessToken');\r\n\r\n            const response = await fetch(this.baseUrl, {\r\n                method: 'GET',\r\n                headers: {\r\n                    'Content-Type': 'application/json',\r\n                    'Authorization': `Bearer ${token}`,\r\n                },\r\n            });\r\n\r\n            if (!response.ok) {\r\n                throw new Error(`HTTP error! status: ${response.status}`);\r\n            }\r\n\r\n            const data: ApiResponse<PatientDetailResponse> = await response.json();\r\n            console.log(data)\r\n            return data;\r\n        } catch (error) {\r\n            console.error('Error fetching patient profile:', error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    // Cập nhật thông tin bệnh nhân\r\n    async updatePatientProfile(request: PatientDetailRequest): Promise<ApiResponse<PatientDetailResponse>> {\r\n        try {\r\n            const token = localStorage.getItem('accessToken');\r\n\r\n            const response = await fetch(this.baseUrl, {\r\n                method: 'PUT',\r\n                headers: {\r\n                    'Content-Type': 'application/json',\r\n                    'Authorization': `Bearer ${token}`,\r\n                },\r\n                body: JSON.stringify(request),\r\n            });\r\n\r\n            if (!response.ok) {\r\n                throw new Error(`HTTP error! status: ${response.status}`);\r\n            }\r\n\r\n            const data: ApiResponse<PatientDetailResponse> = await response.json();\r\n            return data;\r\n        } catch (error) {\r\n            console.error('Error updating patient profile:', error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    // Kích hoạt xác thực 2 bước\r\n    async enable2FA(phoneOrEmail: string): Promise<ApiResponse<string>> {\r\n        try {\r\n\r\n            const response = await fetch(`${API_URL}/api/v1/2fa`, {\r\n                method: 'POST',\r\n                headers: {\r\n                    'Content-Type': 'application/json',\r\n                },\r\n                body: JSON.stringify({\r\n                    phoneOrEmail: phoneOrEmail\r\n                }),\r\n            });\r\n\r\n            if (!response.ok) {\r\n                const errorText = await response.text();\r\n                console.error('Error response:', errorText);\r\n                throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);\r\n            }\r\n\r\n            const data = await response.json();\r\n            console.log('Response data:', data);\r\n            return data;\r\n        } catch (error) {\r\n            console.error('Error enabling 2FA:', error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    // Xác thực mã 2FA\r\n    async verify2FA(phoneOrEmail: string, code: string): Promise<ApiResponse<any>> {\r\n        console.log(phoneOrEmail, code);\r\n        try {\r\n            const response = await fetch(`${API_URL}/api/v1/2fa/verify`, {\r\n                method: 'POST',\r\n                headers: {\r\n                    'Content-Type': 'application/json',\r\n                },\r\n                body: JSON.stringify({\r\n                    phoneOrEmail: phoneOrEmail,\r\n                    code: code\r\n                }),\r\n            });\r\n\r\n            if (!response.ok) {\r\n                throw new Error(`HTTP error! status: ${response.status}`);\r\n            }\r\n\r\n            const data = await response.json();\r\n            return data;\r\n        } catch (error) {\r\n            console.error('Error verifying 2FA:', error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    // Đổi mật khẩu\r\n    async changePassword(currentPassword: string, newPassword: string): Promise<ApiResponse<{ success: boolean }>> {\r\n        try {\r\n            const token = localStorage.getItem('accessToken');\r\n\r\n            const response = await fetch(`${this.baseUrl}/change-password`, {\r\n                method: 'POST',\r\n                headers: {\r\n                    'Content-Type': 'application/json',\r\n                    'Authorization': `Bearer ${token}`,\r\n                },\r\n                body: JSON.stringify({\r\n                    currentPassword,\r\n                    newPassword\r\n                }),\r\n            });\r\n\r\n            if (!response.ok) {\r\n                throw new Error(`HTTP error! status: ${response.status}`);\r\n            }\r\n\r\n            const data = await response.json();\r\n            return data;\r\n        } catch (error) {\r\n            console.error('Error changing password:', error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    // Upload avatar\r\n    async uploadAvatar(file: File): Promise<ApiResponse<{ avatarUrl: string }>> {\r\n        try {\r\n            const token = localStorage.getItem('accessToken');\r\n            const formData = new FormData();\r\n            formData.append('avatar', file);\r\n\r\n            const response = await fetch(`${this.baseUrl}/avatar`, {\r\n                method: 'POST',\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`,\r\n                },\r\n                body: formData,\r\n            });\r\n\r\n            if (!response.ok) {\r\n                throw new Error(`HTTP error! status: ${response.status}`);\r\n            }\r\n\r\n            const data = await response.json();\r\n            return data;\r\n        } catch (error) {\r\n            console.error('Error uploading avatar:', error);\r\n            throw error;\r\n        }\r\n    }\r\n}\r\n\r\nexport const patientService = new PatientService();\r\n"], "names": [], "mappings": ";;;AAAA;;AAIA,MAAM;IACM,UAAU,GAAG,0HAAA,CAAA,UAAO,CAAC,gBAAgB,CAAC,CAAC;IAE/C,MAAM,oBAAiE;QACnE,IAAI;YACA,MAAM,QAAQ,aAAa,OAAO,CAAC;YAEnC,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE;gBACvC,QAAQ;gBACR,SAAS;oBACL,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACtC;YACJ;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBACd,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC5D;YAEA,MAAM,OAA2C,MAAM,SAAS,IAAI;YACpE,QAAQ,GAAG,CAAC;YACZ,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACV;IACJ;IAEA,+BAA+B;IAC/B,MAAM,qBAAqB,OAA6B,EAA+C;QACnG,IAAI;YACA,MAAM,QAAQ,aAAa,OAAO,CAAC;YAEnC,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE;gBACvC,QAAQ;gBACR,SAAS;oBACL,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACtC;gBACA,MAAM,KAAK,SAAS,CAAC;YACzB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBACd,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC5D;YAEA,MAAM,OAA2C,MAAM,SAAS,IAAI;YACpE,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACV;IACJ;IAEA,4BAA4B;IAC5B,MAAM,UAAU,YAAoB,EAAgC;QAChE,IAAI;YAEA,MAAM,WAAW,MAAM,MAAM,GAAG,0HAAA,CAAA,UAAO,CAAC,WAAW,CAAC,EAAE;gBAClD,QAAQ;gBACR,SAAS;oBACL,gBAAgB;gBACpB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACjB,cAAc;gBAClB;YACJ;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBACd,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,mBAAmB;gBACjC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,WAAW;YACnF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,kBAAkB;YAC9B,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM;QACV;IACJ;IAEA,kBAAkB;IAClB,MAAM,UAAU,YAAoB,EAAE,IAAY,EAA6B;QAC3E,QAAQ,GAAG,CAAC,cAAc;QAC1B,IAAI;YACA,MAAM,WAAW,MAAM,MAAM,GAAG,0HAAA,CAAA,UAAO,CAAC,kBAAkB,CAAC,EAAE;gBACzD,QAAQ;gBACR,SAAS;oBACL,gBAAgB;gBACpB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACjB,cAAc;oBACd,MAAM;gBACV;YACJ;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBACd,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC5D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACV;IACJ;IAEA,eAAe;IACf,MAAM,eAAe,eAAuB,EAAE,WAAmB,EAA8C;QAC3G,IAAI;YACA,MAAM,QAAQ,aAAa,OAAO,CAAC;YAEnC,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE;gBAC5D,QAAQ;gBACR,SAAS;oBACL,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACtC;gBACA,MAAM,KAAK,SAAS,CAAC;oBACjB;oBACA;gBACJ;YACJ;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBACd,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC5D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACV;IACJ;IAEA,gBAAgB;IAChB,MAAM,aAAa,IAAU,EAA+C;QACxE,IAAI;YACA,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,UAAU;YAE1B,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBACnD,QAAQ;gBACR,SAAS;oBACL,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACtC;gBACA,MAAM;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBACd,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC5D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACV;IACJ;AACJ;AAEO,MAAM,iBAAiB,IAAI", "debugId": null}}, {"offset": {"line": 408, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/hooks/usePatientProfile.ts"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { PatientDetailResponse, PatientDetailRequest, Gender } from '@/types/user';\nimport { patientService } from '@/services/patientService';\n\ninterface UsePatientProfileReturn {\n    patient: PatientDetailResponse | null;\n    loading: boolean;\n    saving: boolean;\n    error: string | null;\n    loadProfile: () => Promise<void>;\n    updateProfile: (data: PatientDetailRequest) => Promise<boolean>;\n    refreshProfile: () => Promise<void>;\n}\n\nexport const usePatientProfile = (): UsePatientProfileReturn => {\n    const [patient, setPatient] = useState<PatientDetailResponse | null>(null);\n    const [loading, setLoading] = useState(true);\n    const [saving, setSaving] = useState(false);\n    const [error, setError] = useState<string | null>(null);\n\n    const loadProfile = async () => {\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await patientService.getPatientProfile();\n            \n            if (response.code === 200 && response.result) {\n                setPatient(response.result);\n            } else {\n                setError(response.message || 'Không thể tải thông tin hồ sơ');\n            }\n        } catch (err) {\n            console.error('Error loading patient profile:', err);\n            setError('Không thể tải thông tin hồ sơ');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const updateProfile = async (data: PatientDetailRequest): Promise<boolean> => {\n        try {\n            setSaving(true);\n            setError(null);\n            const response = await patientService.updatePatientProfile(data);\n            \n            if (response.code === 200 && response.result) {\n                setPatient(response.result);\n                return true;\n            } else {\n                setError(response.message || 'Không thể cập nhật thông tin');\n                return false;\n            }\n        } catch (err) {\n            console.error('Error updating profile:', err);\n            setError('Không thể cập nhật thông tin');\n            return false;\n        } finally {\n            setSaving(false);\n        }\n    };\n\n    const refreshProfile = async () => {\n        await loadProfile();\n    };\n\n    useEffect(() => {\n        loadProfile();\n    }, []);\n\n    return {\n        patient,\n        loading,\n        saving,\n        error,\n        loadProfile,\n        updateProfile,\n        refreshProfile,\n    };\n};\n"], "names": [], "mappings": ";;;AAAA;AAEA;;;;AAYO,MAAM,oBAAoB;;IAC7B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgC;IACrE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,cAAc;QAChB,IAAI;YACA,WAAW;YACX,SAAS;YACT,MAAM,WAAW,MAAM,oIAAA,CAAA,iBAAc,CAAC,iBAAiB;YAEvD,IAAI,SAAS,IAAI,KAAK,OAAO,SAAS,MAAM,EAAE;gBAC1C,WAAW,SAAS,MAAM;YAC9B,OAAO;gBACH,SAAS,SAAS,OAAO,IAAI;YACjC;QACJ,EAAE,OAAO,KAAK;YACV,QAAQ,KAAK,CAAC,kCAAkC;YAChD,SAAS;QACb,SAAU;YACN,WAAW;QACf;IACJ;IAEA,MAAM,gBAAgB,OAAO;QACzB,IAAI;YACA,UAAU;YACV,SAAS;YACT,MAAM,WAAW,MAAM,oIAAA,CAAA,iBAAc,CAAC,oBAAoB,CAAC;YAE3D,IAAI,SAAS,IAAI,KAAK,OAAO,SAAS,MAAM,EAAE;gBAC1C,WAAW,SAAS,MAAM;gBAC1B,OAAO;YACX,OAAO;gBACH,SAAS,SAAS,OAAO,IAAI;gBAC7B,OAAO;YACX;QACJ,EAAE,OAAO,KAAK;YACV,QAAQ,KAAK,CAAC,2BAA2B;YACzC,SAAS;YACT,OAAO;QACX,SAAU;YACN,UAAU;QACd;IACJ;IAEA,MAAM,iBAAiB;QACnB,MAAM;IACV;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACN;QACJ;sCAAG,EAAE;IAEL,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;IACJ;AACJ;GAhEa", "debugId": null}}, {"offset": {"line": 487, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/profile/TwoFactorAuth.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Shield, QrC<PERSON>, CheckCircle, Copy, Loader2 } from 'lucide-react';\nimport { patientService } from '@/services/patientService';\n\ninterface TwoFactorAuthProps {\n    onShowToast: (message: string, type: 'success' | 'error' | 'info') => void;\n    userEmail?: string;\n    is2FAEnabled?: boolean;\n}\n\nconst TwoFactorAuth: React.FC<TwoFactorAuthProps> = ({ onShowToast, userEmail, is2FAEnabled = false }) => {\n    const [twoFactorEnabled, setTwoFactorEnabled] = useState(is2FAEnabled);\n    const [qrCode, setQrCode] = useState('');\n    const [verificationCode, setVerificationCode] = useState('');\n    const [loading, setLoading] = useState(false);\n\n    // Cập nhật trạng thái khi prop thay đổi\n    React.useEffect(() => {\n        setTwoFactorEnabled(is2FAEnabled);\n    }, [is2FAEnabled]);\n\n    // Kích hoạt 2FA\n    const handleEnable2FA = async () => {\n        if (!userEmail) {\n            onShowToast('Không tìm thấy email người dùng', 'error');\n            return;\n        }\n\n        try {\n            console.log(userEmail)\n            setLoading(true);\n            console.log('Sending 2FA request with email:', userEmail);\n            const response = await patientService.enable2FA(userEmail);\n            console.log('2FA response:', response);\n\n            if (response.code === 200 && response.result) {\n                // response.result là base64 QR code\n                console.log('QR Code data received:', response.result.substring(0, 100) + '...');\n\n                // Kiểm tra xem base64 string đã có prefix chưa\n                let qrCodeData = response.result;\n                if (!qrCodeData.startsWith('data:image/')) {\n                    qrCodeData = `data:image/png;base64,${qrCodeData}`;\n                }\n\n                setQrCode(qrCodeData);\n                onShowToast('QR Code đã được tạo thành công!', 'success');\n            } else {\n                console.error('Invalid response:', response);\n                onShowToast('Không thể kích hoạt xác thực 2 bước', 'error');\n            }\n        } catch (error) {\n            console.error('Error enabling 2FA:', error);\n            onShowToast('Không thể kích hoạt xác thực 2 bước', 'error');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    // Xác thực 2FA\n    const handleVerify2FA = async () => {\n        if (verificationCode.length !== 6) {\n            onShowToast('Mã xác thực phải có 6 chữ số!', 'error');\n            return;\n        }\n\n        if (!userEmail) {\n            onShowToast('Không tìm thấy email người dùng', 'error');\n            return;\n        }\n\n        try {\n            setLoading(true);\n            const response = await patientService.verify2FA(userEmail, verificationCode);\n            if (response.code === 200) {\n                setTwoFactorEnabled(true);\n                onShowToast('Kích hoạt xác thực 2 bước thành công!', 'success');\n                setQrCode('');\n                setVerificationCode('');\n            } else {\n                onShowToast('Mã xác thực không đúng!', 'error');\n            }\n        } catch (error) {\n            console.error('Error verifying 2FA:', error);\n            onShowToast('Không thể xác thực mã 2FA', 'error');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    // Tắt 2FA\n    const handleDisable2FA = async () => {\n        setTwoFactorEnabled(false);\n        setQrCode('');\n        setVerificationCode('');\n        onShowToast('Đã tắt xác thực 2 bước!', 'info');\n    };\n\n    return (\n        <div className=\"p-6\">\n            <h2 className=\"text-lg font-semibold text-gray-900 mb-6\">Xác thực hai bước</h2>\n\n            {!twoFactorEnabled ? (\n                <div className=\"space-y-6\">\n                    <div className=\"flex items-start space-x-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg\">\n                        <Shield className=\"w-6 h-6 text-yellow-600 mt-0.5\" />\n                        <div>\n                            <h3 className=\"font-medium text-yellow-800\">Chưa bảo mật</h3>\n                            <p className=\"text-sm text-yellow-700 mt-1\">\n                                Tài khoản của bạn chưa được bảo vệ bằng xác thực hai bước.\n                                Hãy kích hoạt ngay để tăng cường bảo mật.\n                            </p>\n                        </div>\n                    </div>\n\n                    {!qrCode ? (\n                        <button\n                            onClick={handleEnable2FA}\n                            disabled={loading}\n                            className=\"inline-flex items-center px-4 py-2 bg-gray-900 text-white text-sm font-medium rounded-md hover:bg-gray-800 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed\"\n                        >\n                            {loading ? (\n                                <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n                            ) : (\n                                <QrCode className=\"w-4 h-4 mr-2\" />\n                            )}\n                            {loading ? 'Đang thiết lập...' : 'Bật xác thực 2 bước'}\n                        </button>\n                    ) : (\n                        <div className=\"space-y-6\">\n                            <div className=\"text-center\">\n                                <h3 className=\"font-medium text-gray-900 mb-4\">Quét mã QR bằng ứng dụng xác thực</h3>\n                                <div className=\"inline-block p-4 bg-white border border-gray-200 rounded-lg\">\n                                    <img\n                                        src={qrCode}\n                                        alt=\"QR Code\"\n                                        className=\"w-48 h-48\"\n                                        onLoad={() => console.log('QR Code image loaded successfully')}\n                                        onError={(e) => console.error('QR Code image failed to load:', e)}\n                                    />\n                                </div>\n                                <p className=\"text-sm text-gray-600 mt-2\">\n                                    Sử dụng ứng dụng Google Authenticator, Authy hoặc tương tự\n                                </p>\n                                {/* Debug info */}\n                                <div className=\"mt-2 text-xs text-gray-400\">\n                                    QR Code length: {qrCode.length} | Starts with: {qrCode.substring(0, 50)}...\n                                </div>\n                            </div>\n\n                            <div className=\"max-w-md mx-auto\">\n                                <h3 className=\"font-medium text-gray-900 mb-3\">Nhập mã xác thực</h3>\n                                <div className=\"flex items-center space-x-3\">\n                                    <input\n                                        type=\"text\"\n                                        value={verificationCode}\n                                        onChange={(e) => setVerificationCode(e.target.value)}\n                                        placeholder=\"Nhập mã 6 chữ số\"\n                                        maxLength={6}\n                                        className=\"flex-1 px-3 py-2 bg-gray-50 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:bg-white transition-all duration-200\"\n                                    />\n                                    <button\n                                        onClick={handleVerify2FA}\n                                        disabled={verificationCode.length !== 6 || loading}\n                                        className=\"px-4 py-2 bg-gray-900 text-white text-sm font-medium rounded-md hover:bg-gray-800 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed\"\n                                    >\n                                        {loading ? (\n                                            <Loader2 className=\"w-4 h-4 animate-spin\" />\n                                        ) : (\n                                            'Xác thực'\n                                        )}\n                                    </button>\n                                </div>\n                                <button\n                                    onClick={() => {\n                                        setQrCode('');\n                                        setVerificationCode('');\n                                    }}\n                                    className=\"mt-3 text-sm text-gray-600 hover:text-gray-800\"\n                                >\n                                    ← Hủy\n                                </button>\n                            </div>\n                        </div>\n                    )}\n                </div>\n            ) : (\n                <div className=\"text-center py-12\">\n                    <div className=\"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                        <CheckCircle className=\"w-10 h-10 text-green-600\" />\n                    </div>\n                    <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Xác thực 2 bước đã được kích hoạt</h3>\n                    <p className=\"text-gray-600 mb-6\">Tài khoản của bạn hiện được bảo vệ bằng xác thực hai bước.</p>\n                    <button\n                        onClick={handleDisable2FA}\n                        disabled={loading}\n                        className=\"inline-flex items-center px-4 py-2 bg-gray-900 text-white text-sm font-medium rounded-md hover:bg-gray-800 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed\"\n                    >\n                        {loading ? (\n                            <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n                        ) : null}\n                        {loading ? 'Đang tắt...' : 'Tắt xác thực 2 bước'}\n                    </button>\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default TwoFactorAuth;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AACA;;;;;;AAQA,MAAM,gBAA8C,CAAC,EAAE,WAAW,EAAE,SAAS,EAAE,eAAe,KAAK,EAAE;;IACjG,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,wCAAwC;IACxC,6JAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACZ,oBAAoB;QACxB;kCAAG;QAAC;KAAa;IAEjB,gBAAgB;IAChB,MAAM,kBAAkB;QACpB,IAAI,CAAC,WAAW;YACZ,YAAY,mCAAmC;YAC/C;QACJ;QAEA,IAAI;YACA,QAAQ,GAAG,CAAC;YACZ,WAAW;YACX,QAAQ,GAAG,CAAC,mCAAmC;YAC/C,MAAM,WAAW,MAAM,oIAAA,CAAA,iBAAc,CAAC,SAAS,CAAC;YAChD,QAAQ,GAAG,CAAC,iBAAiB;YAE7B,IAAI,SAAS,IAAI,KAAK,OAAO,SAAS,MAAM,EAAE;gBAC1C,oCAAoC;gBACpC,QAAQ,GAAG,CAAC,0BAA0B,SAAS,MAAM,CAAC,SAAS,CAAC,GAAG,OAAO;gBAE1E,+CAA+C;gBAC/C,IAAI,aAAa,SAAS,MAAM;gBAChC,IAAI,CAAC,WAAW,UAAU,CAAC,gBAAgB;oBACvC,aAAa,CAAC,sBAAsB,EAAE,YAAY;gBACtD;gBAEA,UAAU;gBACV,YAAY,mCAAmC;YACnD,OAAO;gBACH,QAAQ,KAAK,CAAC,qBAAqB;gBACnC,YAAY,uCAAuC;YACvD;QACJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,uBAAuB;YACrC,YAAY,uCAAuC;QACvD,SAAU;YACN,WAAW;QACf;IACJ;IAEA,eAAe;IACf,MAAM,kBAAkB;QACpB,IAAI,iBAAiB,MAAM,KAAK,GAAG;YAC/B,YAAY,iCAAiC;YAC7C;QACJ;QAEA,IAAI,CAAC,WAAW;YACZ,YAAY,mCAAmC;YAC/C;QACJ;QAEA,IAAI;YACA,WAAW;YACX,MAAM,WAAW,MAAM,oIAAA,CAAA,iBAAc,CAAC,SAAS,CAAC,WAAW;YAC3D,IAAI,SAAS,IAAI,KAAK,KAAK;gBACvB,oBAAoB;gBACpB,YAAY,yCAAyC;gBACrD,UAAU;gBACV,oBAAoB;YACxB,OAAO;gBACH,YAAY,2BAA2B;YAC3C;QACJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,wBAAwB;YACtC,YAAY,6BAA6B;QAC7C,SAAU;YACN,WAAW;QACf;IACJ;IAEA,UAAU;IACV,MAAM,mBAAmB;QACrB,oBAAoB;QACpB,UAAU;QACV,oBAAoB;QACpB,YAAY,2BAA2B;IAC3C;IAEA,qBACI,6LAAC;QAAI,WAAU;;0BACX,6LAAC;gBAAG,WAAU;0BAA2C;;;;;;YAExD,CAAC,iCACE,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAI,WAAU;;0CACX,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;;kDACG,6LAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,6LAAC;wCAAE,WAAU;kDAA+B;;;;;;;;;;;;;;;;;;oBAOnD,CAAC,uBACE,6LAAC;wBACG,SAAS;wBACT,UAAU;wBACV,WAAU;;4BAET,wBACG,6LAAC,oNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;qDAEnB,6LAAC,6MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAErB,UAAU,sBAAsB;;;;;;6CAGrC,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAG,WAAU;kDAAiC;;;;;;kDAC/C,6LAAC;wCAAI,WAAU;kDACX,cAAA,6LAAC;4CACG,KAAK;4CACL,KAAI;4CACJ,WAAU;4CACV,QAAQ,IAAM,QAAQ,GAAG,CAAC;4CAC1B,SAAS,CAAC,IAAM,QAAQ,KAAK,CAAC,iCAAiC;;;;;;;;;;;kDAGvE,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAI1C,6LAAC;wCAAI,WAAU;;4CAA6B;4CACvB,OAAO,MAAM;4CAAC;4CAAiB,OAAO,SAAS,CAAC,GAAG;4CAAI;;;;;;;;;;;;;0CAIhF,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAG,WAAU;kDAAiC;;;;;;kDAC/C,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDACG,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;gDACnD,aAAY;gDACZ,WAAW;gDACX,WAAU;;;;;;0DAEd,6LAAC;gDACG,SAAS;gDACT,UAAU,iBAAiB,MAAM,KAAK,KAAK;gDAC3C,WAAU;0DAET,wBACG,6LAAC,oNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;2DAEnB;;;;;;;;;;;;kDAIZ,6LAAC;wCACG,SAAS;4CACL,UAAU;4CACV,oBAAoB;wCACxB;wCACA,WAAU;kDACb;;;;;;;;;;;;;;;;;;;;;;;qCAQjB,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC,8NAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;kCAE3B,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,6LAAC;wBACG,SAAS;wBACT,UAAU;wBACV,WAAU;;4BAET,wBACG,6LAAC,oNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;uCACnB;4BACH,UAAU,gBAAgB;;;;;;;;;;;;;;;;;;;AAMnD;GArMM;KAAA;uCAuMS", "debugId": null}}, {"offset": {"line": 878, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/profile/ChangePassword.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Save, Loader2 } from 'lucide-react';\nimport { patientService } from '@/services/patientService';\n\ninterface ChangePasswordProps {\n    onShowToast: (message: string, type: 'success' | 'error' | 'info') => void;\n}\n\nconst ChangePassword: React.FC<ChangePasswordProps> = ({ onShowToast }) => {\n    const [currentPassword, setCurrentPassword] = useState('');\n    const [newPassword, setNewPassword] = useState('');\n    const [confirmPassword, setConfirmPassword] = useState('');\n    const [loading, setLoading] = useState(false);\n\n    // Xử lý đổi mật khẩu\n    const handleChangePassword = async () => {\n        if (!currentPassword || !newPassword || !confirmPassword) {\n            onShowToast('Vui lòng điền đầy đủ thông tin!', 'error');\n            return;\n        }\n        if (newPassword !== confirmPassword) {\n            onShowToast('<PERSON>ật khẩu mới và xác nhận không khớp!', 'error');\n            return;\n        }\n        if (newPassword.length < 8) {\n            onShowToast('Mật khẩu mới phải có ít nhất 8 ký tự!', 'error');\n            return;\n        }\n\n        try {\n            setLoading(true);\n            const response = await patientService.changePassword(currentPassword, newPassword);\n            if (response.code === 200 && response.result?.success) {\n                setCurrentPassword('');\n                setNewPassword('');\n                setConfirmPassword('');\n                onShowToast('Đổi mật khẩu thành công!');\n            } else {\n                onShowToast(response.message || 'Không thể đổi mật khẩu', 'error');\n            }\n        } catch (error) {\n            console.error('Error changing password:', error);\n            onShowToast('Không thể đổi mật khẩu', 'error');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const isFormValid = currentPassword && newPassword && confirmPassword && newPassword === confirmPassword;\n\n    return (\n        <div className=\"p-6\">\n            <h2 className=\"text-lg font-semibold text-gray-900 mb-6\">Đổi mật khẩu</h2>\n\n            <div className=\"max-w-md space-y-4\">\n                <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Mật khẩu hiện tại\n                    </label>\n                    <input\n                        type=\"password\"\n                        value={currentPassword}\n                        onChange={(e) => setCurrentPassword(e.target.value)}\n                        className=\"w-full px-3 py-2 bg-gray-50 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:bg-white transition-all duration-200\"\n                        placeholder=\"Nhập mật khẩu hiện tại\"\n                        disabled={loading}\n                    />\n                </div>\n\n                <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Mật khẩu mới\n                    </label>\n                    <input\n                        type=\"password\"\n                        value={newPassword}\n                        onChange={(e) => setNewPassword(e.target.value)}\n                        className=\"w-full px-3 py-2 bg-gray-50 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:bg-white transition-all duration-200\"\n                        placeholder=\"Nhập mật khẩu mới\"\n                        disabled={loading}\n                    />\n                    {newPassword && newPassword.length < 8 && (\n                        <p className=\"text-sm text-red-600 mt-1\">Mật khẩu phải có ít nhất 8 ký tự</p>\n                    )}\n                </div>\n\n                <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Xác nhận mật khẩu mới\n                    </label>\n                    <input\n                        type=\"password\"\n                        value={confirmPassword}\n                        onChange={(e) => setConfirmPassword(e.target.value)}\n                        className=\"w-full px-3 py-2 bg-gray-50 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:bg-white transition-all duration-200\"\n                        placeholder=\"Nhập lại mật khẩu mới\"\n                        disabled={loading}\n                    />\n                    {confirmPassword && newPassword !== confirmPassword && (\n                        <p className=\"text-sm text-red-600 mt-1\">Mật khẩu xác nhận không khớp</p>\n                    )}\n                </div>\n\n                <div className=\"pt-4\">\n                    <button\n                        onClick={handleChangePassword}\n                        disabled={!isFormValid || loading}\n                        className=\"inline-flex items-center px-4 py-2 bg-gray-900 text-white text-sm font-medium rounded-md hover:bg-gray-800 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed\"\n                    >\n                        {loading ? (\n                            <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n                        ) : (\n                            <Save className=\"w-4 h-4 mr-2\" />\n                        )}\n                        {loading ? 'Đang cập nhật...' : 'Cập nhật mật khẩu'}\n                    </button>\n                </div>\n\n                <div className=\"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg\">\n                    <h4 className=\"font-medium text-blue-900 mb-2\">Lưu ý bảo mật:</h4>\n                    <ul className=\"text-sm text-blue-800 space-y-1\">\n                        <li>• Mật khẩu nên có ít nhất 8 ký tự</li>\n                        <li>• Bao gồm chữ hoa, chữ thường và số</li>\n                        <li>• Không sử dụng thông tin cá nhân dễ đoán</li>\n                        <li>• Thay đổi mật khẩu định kỳ</li>\n                        <li>• Không chia sẻ mật khẩu với người khác</li>\n                    </ul>\n                </div>\n            </div>\n        </div>\n    );\n};\n\nexport default ChangePassword;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;;;;;;AAMA,MAAM,iBAAgD,CAAC,EAAE,WAAW,EAAE;;IAClE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,qBAAqB;IACrB,MAAM,uBAAuB;QACzB,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,iBAAiB;YACtD,YAAY,mCAAmC;YAC/C;QACJ;QACA,IAAI,gBAAgB,iBAAiB;YACjC,YAAY,wCAAwC;YACpD;QACJ;QACA,IAAI,YAAY,MAAM,GAAG,GAAG;YACxB,YAAY,yCAAyC;YACrD;QACJ;QAEA,IAAI;YACA,WAAW;YACX,MAAM,WAAW,MAAM,oIAAA,CAAA,iBAAc,CAAC,cAAc,CAAC,iBAAiB;YACtE,IAAI,SAAS,IAAI,KAAK,OAAO,SAAS,MAAM,EAAE,SAAS;gBACnD,mBAAmB;gBACnB,eAAe;gBACf,mBAAmB;gBACnB,YAAY;YAChB,OAAO;gBACH,YAAY,SAAS,OAAO,IAAI,0BAA0B;YAC9D;QACJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,YAAY,0BAA0B;QAC1C,SAAU;YACN,WAAW;QACf;IACJ;IAEA,MAAM,cAAc,mBAAmB,eAAe,mBAAmB,gBAAgB;IAEzF,qBACI,6LAAC;QAAI,WAAU;;0BACX,6LAAC;gBAAG,WAAU;0BAA2C;;;;;;0BAEzD,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;;0CACG,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCACG,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;gCAClD,WAAU;gCACV,aAAY;gCACZ,UAAU;;;;;;;;;;;;kCAIlB,6LAAC;;0CACG,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCACG,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,WAAU;gCACV,aAAY;gCACZ,UAAU;;;;;;4BAEb,eAAe,YAAY,MAAM,GAAG,mBACjC,6LAAC;gCAAE,WAAU;0CAA4B;;;;;;;;;;;;kCAIjD,6LAAC;;0CACG,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCACG,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;gCAClD,WAAU;gCACV,aAAY;gCACZ,UAAU;;;;;;4BAEb,mBAAmB,gBAAgB,iCAChC,6LAAC;gCAAE,WAAU;0CAA4B;;;;;;;;;;;;kCAIjD,6LAAC;wBAAI,WAAU;kCACX,cAAA,6LAAC;4BACG,SAAS;4BACT,UAAU,CAAC,eAAe;4BAC1B,WAAU;;gCAET,wBACG,6LAAC,oNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;yDAEnB,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAEnB,UAAU,qBAAqB;;;;;;;;;;;;kCAIxC,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,6LAAC;gCAAG,WAAU;;kDACV,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5B;GA3HM;KAAA;uCA6HS", "debugId": null}}, {"offset": {"line": 1164, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/app/%28page%29/%28patient%29/profile/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport { User, Phone, Shield, Edit, Save, X, Camera, Lock, Loader2, AlertCircle, MapPin, Calendar, Mail } from 'lucide-react';\r\nimport { PatientDetailRequest, Gender } from '@/types/user';\r\nimport Toast from '@/components/ui/Toast';\r\nimport { usePatientProfile } from '@/hooks/usePatientProfile';\r\nimport TwoFactorAuth from '@/components/profile/TwoFactorAuth';\r\nimport ChangePassword from '@/components/profile/ChangePassword';\r\nimport { patientService } from '@/services/patientService';\r\n\r\n\r\n\r\nconst PatientProfile: React.FC = () => {\r\n    const { patient, loading, saving, updateProfile, refreshProfile } = usePatientProfile();\r\n    const [activeTab, setActiveTab] = useState<'profile' | '2fa' | 'password'>('profile');\r\n    const [editingProfile, setEditingProfile] = useState(false);\r\n    const [editForm, setEditForm] = useState<PatientDetailRequest>({\r\n        firstName: '',\r\n        lastName: '',\r\n        phone: '',\r\n        email: '',\r\n        dob: '',\r\n        gender: Gender.Male,\r\n        address: ''\r\n    });\r\n    const [avatarPreview, setAvatarPreview] = useState<string | null>(null);\r\n    const [uploadingAvatar, setUploadingAvatar] = useState(false);\r\n\r\n    // Toast state\r\n    const [toast, setToast] = useState({ show: false, message: '', type: 'success' as 'success' | 'error' | 'info' });\r\n\r\n    const showToast = (message: string, type: 'success' | 'error' | 'info' = 'success') => {\r\n        setToast({ show: true, message, type });\r\n        setTimeout(() => setToast({ show: false, message: '', type: 'success' }), 3000);\r\n    };\r\n\r\n    // Cập nhật editForm khi patient data thay đổi\r\n    React.useEffect(() => {\r\n        if (patient) {\r\n            setEditForm({\r\n                firstName: patient.firstName || '',\r\n                lastName: patient.lastName || '',\r\n                phone: patient.phone || '',\r\n                email: patient.email || '',\r\n                dob: patient.dob || '',\r\n                gender: patient.gender ?? Gender.Male,\r\n                address: patient.address || ''\r\n            });\r\n        }\r\n    }, [patient]);\r\n\r\n    // Hàm định dạng ngày\r\n    const formatDate = (dateString: string): string => {\r\n        return new Date(dateString).toLocaleDateString('vi-VN', {\r\n            day: '2-digit',\r\n            month: '2-digit',\r\n            year: 'numeric',\r\n        });\r\n    };\r\n\r\n    // Hàm lấy văn bản giới tính\r\n    const getGenderText = (gender?: Gender): string => {\r\n        switch (gender) {\r\n            case Gender.Male:\r\n                return 'Nam';\r\n            case Gender.Female:\r\n                return 'Nữ';\r\n            case Gender.Other:\r\n                return 'Khác';\r\n            default:\r\n                return 'Không xác định';\r\n        }\r\n    };\r\n\r\n    // Xử lý chỉnh sửa hồ sơ\r\n    const handleEditProfile = () => {\r\n        if (patient) {\r\n            setEditForm({\r\n                firstName: patient.firstName || '',\r\n                lastName: patient.lastName || '',\r\n                phone: patient.phone || '',\r\n                email: patient.email || '',\r\n                dob: patient.dob || '',\r\n                gender: patient.gender ?? Gender.Male,\r\n                address: patient.address || ''\r\n            });\r\n            setEditingProfile(true);\r\n        }\r\n    };\r\n\r\n    const handleCancelEdit = () => {\r\n        if (patient) {\r\n            setEditForm({\r\n                firstName: patient.firstName || '',\r\n                lastName: patient.lastName || '',\r\n                phone: patient.phone || '',\r\n                email: patient.email || '',\r\n                dob: patient.dob || '',\r\n                gender: patient.gender ?? Gender.Male,\r\n                address: patient.address || ''\r\n            });\r\n        }\r\n        setEditingProfile(false);\r\n    };\r\n\r\n    const handleSaveProfile = async () => {\r\n        const success = await updateProfile(editForm);\r\n        if (success) {\r\n            setEditingProfile(false);\r\n            showToast('Cập nhật thông tin thành công!');\r\n        } else {\r\n            showToast('Không thể cập nhật thông tin', 'error');\r\n        }\r\n    };\r\n\r\n    // Xử lý thay đổi input\r\n    const handleInputChange = (field: keyof PatientDetailRequest, value: string | Gender) => {\r\n        setEditForm((prev) => ({\r\n            ...prev,\r\n            [field]: value,\r\n        }));\r\n    };\r\n\r\n    // Xử lý upload avatar\r\n    const handleAvatarChange = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n        const file = event.target.files?.[0];\r\n        if (file) {\r\n            // Kiểm tra loại file\r\n            if (!file.type.startsWith('image/')) {\r\n                showToast('Vui lòng chọn file ảnh!', 'error');\r\n                return;\r\n            }\r\n\r\n            // Kiểm tra kích thước file (max 5MB)\r\n            if (file.size > 5 * 1024 * 1024) {\r\n                showToast('Kích thước ảnh không được vượt quá 5MB!', 'error');\r\n                return;\r\n            }\r\n\r\n            // Tạo preview\r\n            const reader = new FileReader();\r\n            reader.onload = (e) => {\r\n                setAvatarPreview(e.target?.result as string);\r\n            };\r\n            reader.readAsDataURL(file);\r\n\r\n            // Upload avatar (giả lập - bạn có thể thay thế bằng API thực tế)\r\n            uploadAvatar(file);\r\n        }\r\n    };\r\n\r\n    const uploadAvatar = async (file: File) => {\r\n        try {\r\n            setUploadingAvatar(true);\r\n\r\n            // Upload avatar qua API\r\n            const response = await patientService.uploadAvatar(file);\r\n\r\n            if (response.code === 200 && response.result) {\r\n                showToast('Cập nhật ảnh đại diện thành công!');\r\n                // Refresh profile để lấy avatar mới\r\n                await refreshProfile();\r\n                setAvatarPreview(null); // Clear preview sau khi upload thành công\r\n            } else {\r\n                throw new Error(response.message || 'Upload failed');\r\n            }\r\n        } catch (error) {\r\n            console.error('Error uploading avatar:', error);\r\n            showToast('Không thể cập nhật ảnh đại diện', 'error');\r\n            setAvatarPreview(null);\r\n        } finally {\r\n            setUploadingAvatar(false);\r\n        }\r\n    };\r\n\r\n\r\n\r\n    const tabs = [\r\n        { id: 'profile', label: 'Thông tin cá nhân', icon: User },\r\n        { id: '2fa', label: 'Bảo mật', icon: Shield },\r\n        { id: 'password', label: 'Đổi mật khẩu', icon: Lock },\r\n    ];\r\n\r\n    // Loading state\r\n    if (loading) {\r\n        return (\r\n            <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\r\n                <div className=\"text-center\">\r\n                    <Loader2 className=\"w-8 h-8 animate-spin text-blue-600 mx-auto mb-4\" />\r\n                    <p className=\"text-gray-600\">Đang tải thông tin hồ sơ...</p>\r\n                </div>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    // Error state\r\n    if (!patient) {\r\n        return (\r\n            <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\r\n                <div className=\"text-center\">\r\n                    <AlertCircle className=\"w-8 h-8 text-red-600 mx-auto mb-4\" />\r\n                    <p className=\"text-gray-600 mb-4\">Không thể tải thông tin hồ sơ</p>\r\n                    <button\r\n                        onClick={refreshProfile}\r\n                        className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\"\r\n                    >\r\n                        Thử lại\r\n                    </button>\r\n                </div>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    return (\r\n        <div className=\"min-h-screen bg-gray-100\">\r\n            <Toast {...toast} onClose={() => setToast({ ...toast, show: false })} />\r\n\r\n            <div className=\"max-w-6xl mx-auto px-6 py-8\">\r\n                <div className=\"flex gap-8\">\r\n                    {/* Sidebar */}\r\n                    <div className=\"w-72 flex-shrink-0\">\r\n                        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\">\r\n                            <div className=\"p-6 bg-gray-900 border-b border-gray-200\">\r\n                                <h3 className=\"text-lg font-semibold text-white mb-1\">Cài đặt tài khoản</h3>\r\n                                <p className=\"text-sm text-gray-300\">Quản lý thông tin và bảo mật</p>\r\n                            </div>\r\n                            <nav className=\"p-4\">\r\n                                {tabs.map((tab) => {\r\n                                    const Icon = tab.icon;\r\n                                    return (\r\n                                        <button\r\n                                            key={tab.id}\r\n                                            onClick={() => setActiveTab(tab.id as any)}\r\n                                            className={`w-full flex items-center px-4 py-3 rounded-lg text-sm font-medium transition-all duration-200 mb-2 group ${activeTab === tab.id\r\n                                                ? 'bg-gray-900 text-white shadow-md'\r\n                                                : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'\r\n                                                }`}\r\n                                        >\r\n                                            <Icon className={`w-5 h-5 mr-4 transition-colors duration-200 ${activeTab === tab.id ? 'text-white' : 'text-gray-500 group-hover:text-gray-700'}`} />\r\n                                            <span className=\"flex-1 text-left\">{tab.label}</span>\r\n                                            {activeTab === tab.id && (\r\n                                                <div className=\"w-2 h-2 bg-white rounded-full\"></div>\r\n                                            )}\r\n                                        </button>\r\n                                    );\r\n                                })}\r\n                            </nav>\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* Main Content */}\r\n                    <div className=\"flex-1\">\r\n                        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\r\n                            {/* Profile Tab */}\r\n                            {activeTab === 'profile' && (\r\n                                <div className=\"p-6\">\r\n                                    <div className=\"flex flex-col sm:flex-row sm:items-center justify-between mb-6 pb-4 border-b border-gray-200\">\r\n                                        <div>\r\n                                            <h2 className=\"text-xl font-semibold text-gray-900 mb-1\">Thông tin cá nhân</h2>\r\n                                            <p className=\"text-gray-600 text-sm\">Cập nhật thông tin cá nhân của bạn</p>\r\n                                        </div>\r\n                                        <div className=\"mt-4 sm:mt-0\">\r\n                                            {!editingProfile ? (\r\n                                                <button\r\n                                                    onClick={handleEditProfile}\r\n                                                    className=\"inline-flex items-center px-4 py-2 bg-gray-900 text-white text-sm font-medium rounded-md hover:bg-gray-800 transition-colors duration-200\"\r\n                                                >\r\n                                                    <Edit className=\"w-4 h-4 mr-2\" />\r\n                                                    Chỉnh sửa\r\n                                                </button>\r\n                                            ) : (\r\n                                                <div className=\"flex space-x-3\">\r\n                                                    <button\r\n                                                        onClick={handleCancelEdit}\r\n                                                        className=\"inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-200 transition-colors duration-200\"\r\n                                                    >\r\n                                                        <X className=\"w-4 h-4 mr-2\" />\r\n                                                        Hủy\r\n                                                    </button>\r\n                                                    <button\r\n                                                        onClick={handleSaveProfile}\r\n                                                        disabled={saving}\r\n                                                        className=\"inline-flex items-center px-4 py-2 bg-gray-900 text-white text-sm font-medium rounded-md hover:bg-gray-800 transition-colors duration-200 disabled:bg-gray-400 disabled:cursor-not-allowed\"\r\n                                                    >\r\n                                                        {saving ? (\r\n                                                            <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\r\n                                                        ) : (\r\n                                                            <Save className=\"w-4 h-4 mr-2\" />\r\n                                                        )}\r\n                                                        {saving ? 'Đang lưu...' : 'Lưu'}\r\n                                                    </button>\r\n                                                </div>\r\n                                            )}\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                    {/* Avatar Section */}\r\n                                    <div className=\"flex flex-col sm:flex-row items-center sm:items-start space-y-4 sm:space-y-0 sm:space-x-8 mb-8 pb-8 border-b border-gray-200\">\r\n                                        <div className=\"relative group\">\r\n                                            <div className=\"w-24 h-24 rounded-full overflow-hidden bg-gray-100 shadow-md\">\r\n                                                {avatarPreview ? (\r\n                                                    <img src={avatarPreview} alt=\"Avatar Preview\" className=\"w-full h-full object-cover\" />\r\n                                                ) : patient.avatar ? (\r\n                                                    <img src={patient.avatar} alt=\"Avatar\" className=\"w-full h-full object-cover\" />\r\n                                                ) : (\r\n                                                    <div className=\"w-full h-full flex items-center justify-center\">\r\n                                                        <User className=\"w-12 h-12 text-gray-400\" />\r\n                                                    </div>\r\n                                                )}\r\n                                                {uploadingAvatar && (\r\n                                                    <div className=\"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-full\">\r\n                                                        <Loader2 className=\"w-6 h-6 text-white animate-spin\" />\r\n                                                    </div>\r\n                                                )}\r\n                                            </div>\r\n\r\n                                            {/* Camera button */}\r\n                                            <div className=\"absolute bottom-0 right-0\">\r\n                                                <input\r\n                                                    type=\"file\"\r\n                                                    accept=\"image/*\"\r\n                                                    onChange={handleAvatarChange}\r\n                                                    className=\"hidden\"\r\n                                                    id=\"avatar-upload\"\r\n                                                    disabled={uploadingAvatar}\r\n                                                />\r\n                                                <label\r\n                                                    htmlFor=\"avatar-upload\"\r\n                                                    className={`w-8 h-8 bg-gray-900 rounded-full flex items-center justify-center text-white shadow-md cursor-pointer transition-all duration-200 hover:bg-gray-800 ${uploadingAvatar ? 'opacity-50 cursor-not-allowed' : ''\r\n                                                        }`}\r\n                                                >\r\n                                                    <Camera className=\"w-4 h-4\" />\r\n                                                </label>\r\n                                            </div>\r\n                                        </div>\r\n\r\n                                        <div className=\"flex-1 text-center sm:text-left\">\r\n                                            <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\r\n                                                {patient.firstName} {patient.lastName}\r\n                                            </h3>\r\n                                            <div className=\"space-y-1\">\r\n                                                <p className=\"text-gray-600 flex items-center justify-center sm:justify-start text-sm\">\r\n                                                    <Phone className=\"w-4 h-4 mr-2 text-gray-500\" />\r\n                                                    {patient.phone || 'Chưa cập nhật'}\r\n                                                </p>\r\n                                                <p className=\"text-gray-600 flex items-center justify-center sm:justify-start text-sm\">\r\n                                                    <User className=\"w-4 h-4 mr-2 text-gray-500\" />\r\n                                                    {getGenderText(patient.gender)}\r\n                                                </p>\r\n                                                {patient.address && (\r\n                                                    <p className=\"text-gray-600 flex items-start justify-center sm:justify-start text-sm\">\r\n                                                        <MapPin className=\"w-4 h-4 mr-2 mt-0.5 text-gray-500\" />\r\n                                                        <span className=\"max-w-xs\">{patient.address}</span>\r\n                                                    </p>\r\n                                                )}\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                    {/* Form Fields */}\r\n                                    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n                                        <div className=\"space-y-2\">\r\n                                            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                                                <User className=\"w-4 h-4 inline mr-2 text-gray-500\" />\r\n                                                Họ\r\n                                            </label>\r\n                                            {editingProfile ? (\r\n                                                <input\r\n                                                    type=\"text\"\r\n                                                    value={editForm.firstName || ''}\r\n                                                    onChange={(e) => handleInputChange('firstName', e.target.value)}\r\n                                                    className=\"w-full px-3 py-2 bg-gray-50 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:bg-white transition-all duration-200\"\r\n                                                    placeholder=\"Nhập họ của bạn\"\r\n                                                />\r\n                                            ) : (\r\n                                                <div className=\"px-3 py-2 bg-gray-50 rounded-md\">\r\n                                                    <p className=\"text-gray-900\">{patient.firstName || 'Chưa cập nhật'}</p>\r\n                                                </div>\r\n                                            )}\r\n                                        </div>\r\n\r\n                                        <div className=\"space-y-2\">\r\n                                            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                                                <User className=\"w-4 h-4 inline mr-2 text-gray-500\" />\r\n                                                Tên\r\n                                            </label>\r\n                                            {editingProfile ? (\r\n                                                <input\r\n                                                    type=\"text\"\r\n                                                    value={editForm.lastName || ''}\r\n                                                    onChange={(e) => handleInputChange('lastName', e.target.value)}\r\n                                                    className=\"w-full px-3 py-2 bg-gray-50 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:bg-white transition-all duration-200\"\r\n                                                    placeholder=\"Nhập tên của bạn\"\r\n                                                />\r\n                                            ) : (\r\n                                                <div className=\"px-3 py-2 bg-gray-50 rounded-md\">\r\n                                                    <p className=\"text-gray-900\">{patient.lastName || 'Chưa cập nhật'}</p>\r\n                                                </div>\r\n                                            )}\r\n                                        </div>\r\n\r\n                                        <div className=\"space-y-2\">\r\n                                            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                                                <Phone className=\"w-4 h-4 inline mr-2 text-gray-500\" />\r\n                                                Số điện thoại\r\n                                            </label>\r\n                                            {editingProfile ? (\r\n                                                <input\r\n                                                    type=\"tel\"\r\n                                                    value={editForm.phone || ''}\r\n                                                    onChange={(e) => handleInputChange('phone', e.target.value)}\r\n                                                    className=\"w-full px-3 py-2 bg-gray-50 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:bg-white transition-all duration-200\"\r\n                                                    placeholder=\"Nhập số điện thoại\"\r\n                                                />\r\n                                            ) : (\r\n                                                <div className=\"px-3 py-2 bg-gray-50 rounded-md\">\r\n                                                    <p className=\"text-gray-900\">{patient.phone || 'Chưa cập nhật'}</p>\r\n                                                </div>\r\n                                            )}\r\n                                        </div>\r\n\r\n                                        <div className=\"space-y-2\">\r\n                                            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                                                <Mail className=\"w-4 h-4 inline mr-2 text-gray-500\" />\r\n                                                Email\r\n                                            </label>\r\n                                            {editingProfile ? (\r\n                                                <input\r\n                                                    type=\"email\"\r\n                                                    value={editForm.email || ''}\r\n                                                    onChange={(e) => handleInputChange('email', e.target.value)}\r\n                                                    className=\"w-full px-3 py-2 bg-gray-50 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:bg-white transition-all duration-200\"\r\n                                                    placeholder=\"Nhập email\"\r\n                                                />\r\n                                            ) : (\r\n                                                <div className=\"px-3 py-2 bg-gray-50 rounded-md\">\r\n                                                    <p className=\"text-gray-900\">{patient.email || 'Chưa cập nhật'}</p>\r\n                                                </div>\r\n                                            )}\r\n                                        </div>\r\n\r\n                                        <div className=\"space-y-2\">\r\n                                            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                                                <Calendar className=\"w-4 h-4 inline mr-2 text-gray-500\" />\r\n                                                Ngày sinh\r\n                                            </label>\r\n                                            {editingProfile ? (\r\n                                                <input\r\n                                                    type=\"date\"\r\n                                                    value={editForm.dob}\r\n                                                    onChange={(e) => handleInputChange('dob', e.target.value)}\r\n                                                    className=\"w-full px-3 py-2 bg-gray-50 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:bg-white transition-all duration-200\"\r\n                                                />\r\n                                            ) : (\r\n                                                <div className=\"px-3 py-2 bg-gray-50 rounded-md\">\r\n                                                    <p className=\"text-gray-900\">{formatDate(patient.dob)}</p>\r\n                                                </div>\r\n                                            )}\r\n                                        </div>\r\n\r\n                                        <div className=\"space-y-2\">\r\n                                            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                                                <User className=\"w-4 h-4 inline mr-2 text-gray-500\" />\r\n                                                Giới tính\r\n                                            </label>\r\n                                            {editingProfile ? (\r\n                                                <select\r\n                                                    value={editForm.gender ?? Gender.Male}\r\n                                                    onChange={(e) => handleInputChange('gender', e.target.value as Gender)}\r\n                                                    className=\"w-full px-3 py-2 bg-gray-50 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:bg-white transition-all duration-200\"\r\n                                                >\r\n                                                    <option value={Gender.Male}>Nam</option>\r\n                                                    <option value={Gender.Female}>Nữ</option>\r\n                                                    <option value={Gender.Other}>Khác</option>\r\n                                                </select>\r\n                                            ) : (\r\n                                                <div className=\"px-3 py-2 bg-gray-50 rounded-md\">\r\n                                                    <p className=\"text-gray-900\">{getGenderText(patient.gender)}</p>\r\n                                                </div>\r\n                                            )}\r\n                                        </div>\r\n\r\n                                        <div className=\"md:col-span-2 lg:col-span-3 space-y-2\">\r\n                                            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                                                <MapPin className=\"w-4 h-4 inline mr-2 text-gray-500\" />\r\n                                                Địa chỉ\r\n                                            </label>\r\n                                            {editingProfile ? (\r\n                                                <textarea\r\n                                                    value={editForm.address || ''}\r\n                                                    onChange={(e) => handleInputChange('address', e.target.value)}\r\n                                                    rows={3}\r\n                                                    className=\"w-full px-3 py-2 bg-gray-50 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:bg-white transition-all duration-200 resize-none\"\r\n                                                    placeholder=\"Nhập địa chỉ của bạn\"\r\n                                                />\r\n                                            ) : (\r\n                                                <div className=\"px-3 py-2 bg-gray-50 rounded-md min-h-[80px]\">\r\n                                                    <p className=\"text-gray-900\">{patient.address || 'Chưa cập nhật'}</p>\r\n                                                </div>\r\n                                            )}\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            )}\r\n\r\n                            {/* 2FA Tab */}\r\n                            {activeTab === '2fa' && (\r\n                                <TwoFactorAuth\r\n                                    onShowToast={showToast}\r\n                                    userEmail={patient?.email || patient?.phone || '<EMAIL>'}\r\n                                    is2FAEnabled={patient?.enable2FA || false}\r\n                                />\r\n                            )}\r\n\r\n                            {/* Password Tab */}\r\n                            {activeTab === 'password' && (\r\n                                <ChangePassword onShowToast={showToast} />\r\n                            )}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default PatientProfile;"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAaA,MAAM,iBAA2B;;IAC7B,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,oBAAiB,AAAD;IACpF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkC;IAC3E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;QAC3D,WAAW;QACX,UAAU;QACV,OAAO;QACP,OAAO;QACP,KAAK;QACL,QAAQ,uHAAA,CAAA,SAAM,CAAC,IAAI;QACnB,SAAS;IACb;IACA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,cAAc;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,MAAM;QAAO,SAAS;QAAI,MAAM;IAA0C;IAE/G,MAAM,YAAY,CAAC,SAAiB,OAAqC,SAAS;QAC9E,SAAS;YAAE,MAAM;YAAM;YAAS;QAAK;QACrC,WAAW,IAAM,SAAS;gBAAE,MAAM;gBAAO,SAAS;gBAAI,MAAM;YAAU,IAAI;IAC9E;IAEA,8CAA8C;IAC9C,6JAAA,CAAA,UAAK,CAAC,SAAS;oCAAC;YACZ,IAAI,SAAS;gBACT,YAAY;oBACR,WAAW,QAAQ,SAAS,IAAI;oBAChC,UAAU,QAAQ,QAAQ,IAAI;oBAC9B,OAAO,QAAQ,KAAK,IAAI;oBACxB,OAAO,QAAQ,KAAK,IAAI;oBACxB,KAAK,QAAQ,GAAG,IAAI;oBACpB,QAAQ,QAAQ,MAAM,IAAI,uHAAA,CAAA,SAAM,CAAC,IAAI;oBACrC,SAAS,QAAQ,OAAO,IAAI;gBAChC;YACJ;QACJ;mCAAG;QAAC;KAAQ;IAEZ,qBAAqB;IACrB,MAAM,aAAa,CAAC;QAChB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACpD,KAAK;YACL,OAAO;YACP,MAAM;QACV;IACJ;IAEA,4BAA4B;IAC5B,MAAM,gBAAgB,CAAC;QACnB,OAAQ;YACJ,KAAK,uHAAA,CAAA,SAAM,CAAC,IAAI;gBACZ,OAAO;YACX,KAAK,uHAAA,CAAA,SAAM,CAAC,MAAM;gBACd,OAAO;YACX,KAAK,uHAAA,CAAA,SAAM,CAAC,KAAK;gBACb,OAAO;YACX;gBACI,OAAO;QACf;IACJ;IAEA,wBAAwB;IACxB,MAAM,oBAAoB;QACtB,IAAI,SAAS;YACT,YAAY;gBACR,WAAW,QAAQ,SAAS,IAAI;gBAChC,UAAU,QAAQ,QAAQ,IAAI;gBAC9B,OAAO,QAAQ,KAAK,IAAI;gBACxB,OAAO,QAAQ,KAAK,IAAI;gBACxB,KAAK,QAAQ,GAAG,IAAI;gBACpB,QAAQ,QAAQ,MAAM,IAAI,uHAAA,CAAA,SAAM,CAAC,IAAI;gBACrC,SAAS,QAAQ,OAAO,IAAI;YAChC;YACA,kBAAkB;QACtB;IACJ;IAEA,MAAM,mBAAmB;QACrB,IAAI,SAAS;YACT,YAAY;gBACR,WAAW,QAAQ,SAAS,IAAI;gBAChC,UAAU,QAAQ,QAAQ,IAAI;gBAC9B,OAAO,QAAQ,KAAK,IAAI;gBACxB,OAAO,QAAQ,KAAK,IAAI;gBACxB,KAAK,QAAQ,GAAG,IAAI;gBACpB,QAAQ,QAAQ,MAAM,IAAI,uHAAA,CAAA,SAAM,CAAC,IAAI;gBACrC,SAAS,QAAQ,OAAO,IAAI;YAChC;QACJ;QACA,kBAAkB;IACtB;IAEA,MAAM,oBAAoB;QACtB,MAAM,UAAU,MAAM,cAAc;QACpC,IAAI,SAAS;YACT,kBAAkB;YAClB,UAAU;QACd,OAAO;YACH,UAAU,gCAAgC;QAC9C;IACJ;IAEA,uBAAuB;IACvB,MAAM,oBAAoB,CAAC,OAAmC;QAC1D,YAAY,CAAC,OAAS,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACb,CAAC;IACL;IAEA,sBAAsB;IACtB,MAAM,qBAAqB,CAAC;QACxB,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,MAAM;YACN,qBAAqB;YACrB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;gBACjC,UAAU,2BAA2B;gBACrC;YACJ;YAEA,qCAAqC;YACrC,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;gBAC7B,UAAU,2CAA2C;gBACrD;YACJ;YAEA,cAAc;YACd,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;gBACb,iBAAiB,EAAE,MAAM,EAAE;YAC/B;YACA,OAAO,aAAa,CAAC;YAErB,iEAAiE;YACjE,aAAa;QACjB;IACJ;IAEA,MAAM,eAAe,OAAO;QACxB,IAAI;YACA,mBAAmB;YAEnB,wBAAwB;YACxB,MAAM,WAAW,MAAM,oIAAA,CAAA,iBAAc,CAAC,YAAY,CAAC;YAEnD,IAAI,SAAS,IAAI,KAAK,OAAO,SAAS,MAAM,EAAE;gBAC1C,UAAU;gBACV,oCAAoC;gBACpC,MAAM;gBACN,iBAAiB,OAAO,0CAA0C;YACtE,OAAO;gBACH,MAAM,IAAI,MAAM,SAAS,OAAO,IAAI;YACxC;QACJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,2BAA2B;YACzC,UAAU,mCAAmC;YAC7C,iBAAiB;QACrB,SAAU;YACN,mBAAmB;QACvB;IACJ;IAIA,MAAM,OAAO;QACT;YAAE,IAAI;YAAW,OAAO;YAAqB,MAAM,qMAAA,CAAA,OAAI;QAAC;QACxD;YAAE,IAAI;YAAO,OAAO;YAAW,MAAM,yMAAA,CAAA,SAAM;QAAC;QAC5C;YAAE,IAAI;YAAY,OAAO;YAAgB,MAAM,qMAAA,CAAA,OAAI;QAAC;KACvD;IAED,gBAAgB;IAChB,IAAI,SAAS;QACT,qBACI,6LAAC;YAAI,WAAU;sBACX,cAAA,6LAAC;gBAAI,WAAU;;kCACX,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAI7C;IAEA,cAAc;IACd,IAAI,CAAC,SAAS;QACV,qBACI,6LAAC;YAAI,WAAU;sBACX,cAAA,6LAAC;gBAAI,WAAU;;kCACX,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,6LAAC;wBACG,SAAS;wBACT,WAAU;kCACb;;;;;;;;;;;;;;;;;IAMjB;IAEA,qBACI,6LAAC;QAAI,WAAU;;0BACX,6LAAC,oIAAA,CAAA,UAAK;gBAAE,GAAG,KAAK;gBAAE,SAAS,IAAM,SAAS;wBAAE,GAAG,KAAK;wBAAE,MAAM;oBAAM;;;;;;0BAElE,6LAAC;gBAAI,WAAU;0BACX,cAAA,6LAAC;oBAAI,WAAU;;sCAEX,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAG,WAAU;0DAAwC;;;;;;0DACtD,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,6LAAC;wCAAI,WAAU;kDACV,KAAK,GAAG,CAAC,CAAC;4CACP,MAAM,OAAO,IAAI,IAAI;4CACrB,qBACI,6LAAC;gDAEG,SAAS,IAAM,aAAa,IAAI,EAAE;gDAClC,WAAW,CAAC,yGAAyG,EAAE,cAAc,IAAI,EAAE,GACrI,qCACA,sDACA;;kEAEN,6LAAC;wDAAK,WAAW,CAAC,4CAA4C,EAAE,cAAc,IAAI,EAAE,GAAG,eAAe,2CAA2C;;;;;;kEACjJ,6LAAC;wDAAK,WAAU;kEAAoB,IAAI,KAAK;;;;;;oDAC5C,cAAc,IAAI,EAAE,kBACjB,6LAAC;wDAAI,WAAU;;;;;;;+CAVd,IAAI,EAAE;;;;;wCAcvB;;;;;;;;;;;;;;;;;sCAMZ,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC;gCAAI,WAAU;;oCAEV,cAAc,2BACX,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAI,WAAU;;kEACX,6LAAC;;0EACG,6LAAC;gEAAG,WAAU;0EAA2C;;;;;;0EACzD,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEzC,6LAAC;wDAAI,WAAU;kEACV,CAAC,+BACE,6LAAC;4DACG,SAAS;4DACT,WAAU;;8EAEV,6LAAC,8MAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;iFAIrC,6LAAC;4DAAI,WAAU;;8EACX,6LAAC;oEACG,SAAS;oEACT,WAAU;;sFAEV,6LAAC,+LAAA,CAAA,IAAC;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;8EAGlC,6LAAC;oEACG,SAAS;oEACT,UAAU;oEACV,WAAU;;wEAET,uBACG,6LAAC,oNAAA,CAAA,UAAO;4EAAC,WAAU;;;;;iGAEnB,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEAEnB,SAAS,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;0DAQ9C,6LAAC;gDAAI,WAAU;;kEACX,6LAAC;wDAAI,WAAU;;0EACX,6LAAC;gEAAI,WAAU;;oEACV,8BACG,6LAAC;wEAAI,KAAK;wEAAe,KAAI;wEAAiB,WAAU;;;;;+EACxD,QAAQ,MAAM,iBACd,6LAAC;wEAAI,KAAK,QAAQ,MAAM;wEAAE,KAAI;wEAAS,WAAU;;;;;6FAEjD,6LAAC;wEAAI,WAAU;kFACX,cAAA,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;;;;;;oEAGvB,iCACG,6LAAC;wEAAI,WAAU;kFACX,cAAA,6LAAC,oNAAA,CAAA,UAAO;4EAAC,WAAU;;;;;;;;;;;;;;;;;0EAM/B,6LAAC;gEAAI,WAAU;;kFACX,6LAAC;wEACG,MAAK;wEACL,QAAO;wEACP,UAAU;wEACV,WAAU;wEACV,IAAG;wEACH,UAAU;;;;;;kFAEd,6LAAC;wEACG,SAAQ;wEACR,WAAW,CAAC,oJAAoJ,EAAE,kBAAkB,kCAAkC,IAChN;kFAEN,cAAA,6LAAC,yMAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kEAK9B,6LAAC;wDAAI,WAAU;;0EACX,6LAAC;gEAAG,WAAU;;oEACT,QAAQ,SAAS;oEAAC;oEAAE,QAAQ,QAAQ;;;;;;;0EAEzC,6LAAC;gEAAI,WAAU;;kFACX,6LAAC;wEAAE,WAAU;;0FACT,6LAAC,uMAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;4EAChB,QAAQ,KAAK,IAAI;;;;;;;kFAEtB,6LAAC;wEAAE,WAAU;;0FACT,6LAAC,qMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;4EACf,cAAc,QAAQ,MAAM;;;;;;;oEAEhC,QAAQ,OAAO,kBACZ,6LAAC;wEAAE,WAAU;;0FACT,6LAAC,6MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;0FAClB,6LAAC;gFAAK,WAAU;0FAAY,QAAQ,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAQ/D,6LAAC;gDAAI,WAAU;;kEACX,6LAAC;wDAAI,WAAU;;0EACX,6LAAC;gEAAM,WAAU;;kFACb,6LAAC,qMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAsC;;;;;;;4DAGzD,+BACG,6LAAC;gEACG,MAAK;gEACL,OAAO,SAAS,SAAS,IAAI;gEAC7B,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;gEAC9D,WAAU;gEACV,aAAY;;;;;qFAGhB,6LAAC;gEAAI,WAAU;0EACX,cAAA,6LAAC;oEAAE,WAAU;8EAAiB,QAAQ,SAAS,IAAI;;;;;;;;;;;;;;;;;kEAK/D,6LAAC;wDAAI,WAAU;;0EACX,6LAAC;gEAAM,WAAU;;kFACb,6LAAC,qMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAsC;;;;;;;4DAGzD,+BACG,6LAAC;gEACG,MAAK;gEACL,OAAO,SAAS,QAAQ,IAAI;gEAC5B,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;gEAC7D,WAAU;gEACV,aAAY;;;;;qFAGhB,6LAAC;gEAAI,WAAU;0EACX,cAAA,6LAAC;oEAAE,WAAU;8EAAiB,QAAQ,QAAQ,IAAI;;;;;;;;;;;;;;;;;kEAK9D,6LAAC;wDAAI,WAAU;;0EACX,6LAAC;gEAAM,WAAU;;kFACb,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAAsC;;;;;;;4DAG1D,+BACG,6LAAC;gEACG,MAAK;gEACL,OAAO,SAAS,KAAK,IAAI;gEACzB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;gEAC1D,WAAU;gEACV,aAAY;;;;;qFAGhB,6LAAC;gEAAI,WAAU;0EACX,cAAA,6LAAC;oEAAE,WAAU;8EAAiB,QAAQ,KAAK,IAAI;;;;;;;;;;;;;;;;;kEAK3D,6LAAC;wDAAI,WAAU;;0EACX,6LAAC;gEAAM,WAAU;;kFACb,6LAAC,qMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAsC;;;;;;;4DAGzD,+BACG,6LAAC;gEACG,MAAK;gEACL,OAAO,SAAS,KAAK,IAAI;gEACzB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;gEAC1D,WAAU;gEACV,aAAY;;;;;qFAGhB,6LAAC;gEAAI,WAAU;0EACX,cAAA,6LAAC;oEAAE,WAAU;8EAAiB,QAAQ,KAAK,IAAI;;;;;;;;;;;;;;;;;kEAK3D,6LAAC;wDAAI,WAAU;;0EACX,6LAAC;gEAAM,WAAU;;kFACb,6LAAC,6MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAAsC;;;;;;;4DAG7D,+BACG,6LAAC;gEACG,MAAK;gEACL,OAAO,SAAS,GAAG;gEACnB,UAAU,CAAC,IAAM,kBAAkB,OAAO,EAAE,MAAM,CAAC,KAAK;gEACxD,WAAU;;;;;qFAGd,6LAAC;gEAAI,WAAU;0EACX,cAAA,6LAAC;oEAAE,WAAU;8EAAiB,WAAW,QAAQ,GAAG;;;;;;;;;;;;;;;;;kEAKhE,6LAAC;wDAAI,WAAU;;0EACX,6LAAC;gEAAM,WAAU;;kFACb,6LAAC,qMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAsC;;;;;;;4DAGzD,+BACG,6LAAC;gEACG,OAAO,SAAS,MAAM,IAAI,uHAAA,CAAA,SAAM,CAAC,IAAI;gEACrC,UAAU,CAAC,IAAM,kBAAkB,UAAU,EAAE,MAAM,CAAC,KAAK;gEAC3D,WAAU;;kFAEV,6LAAC;wEAAO,OAAO,uHAAA,CAAA,SAAM,CAAC,IAAI;kFAAE;;;;;;kFAC5B,6LAAC;wEAAO,OAAO,uHAAA,CAAA,SAAM,CAAC,MAAM;kFAAE;;;;;;kFAC9B,6LAAC;wEAAO,OAAO,uHAAA,CAAA,SAAM,CAAC,KAAK;kFAAE;;;;;;;;;;;qFAGjC,6LAAC;gEAAI,WAAU;0EACX,cAAA,6LAAC;oEAAE,WAAU;8EAAiB,cAAc,QAAQ,MAAM;;;;;;;;;;;;;;;;;kEAKtE,6LAAC;wDAAI,WAAU;;0EACX,6LAAC;gEAAM,WAAU;;kFACb,6LAAC,6MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAsC;;;;;;;4DAG3D,+BACG,6LAAC;gEACG,OAAO,SAAS,OAAO,IAAI;gEAC3B,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;gEAC5D,MAAM;gEACN,WAAU;gEACV,aAAY;;;;;qFAGhB,6LAAC;gEAAI,WAAU;0EACX,cAAA,6LAAC;oEAAE,WAAU;8EAAiB,QAAQ,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCASxE,cAAc,uBACX,6LAAC,iJAAA,CAAA,UAAa;wCACV,aAAa;wCACb,WAAW,SAAS,SAAS,SAAS,SAAS;wCAC/C,cAAc,SAAS,aAAa;;;;;;oCAK3C,cAAc,4BACX,6LAAC,kJAAA,CAAA,UAAc;wCAAC,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7D;GAhgBM;;QACkE,oIAAA,CAAA,oBAAiB;;;KADnF;uCAkgBS", "debugId": null}}]}