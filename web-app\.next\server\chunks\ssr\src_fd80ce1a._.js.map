{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/ui/LoadingSpinner.tsx"], "sourcesContent": ["interface LoadingSpinnerProps {\r\n    size?: 'sm' | 'md' | 'lg';\r\n}\r\n\r\nexport const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ size = 'md' }) => {\r\n    const sizeClasses = {\r\n        sm: 'h-4 w-4',\r\n        md: 'h-8 w-8',\r\n        lg: 'h-12 w-12'\r\n    };\r\n\r\n    return (\r\n        <div className=\"flex justify-center items-center\">\r\n            <div className={`animate-spin rounded-full border-t-2 border-b-2 border-blue-600 ${sizeClasses[size]}`}></div>\r\n        </div>\r\n    );\r\n};"], "names": [], "mappings": ";;;;;AAIO,MAAM,iBAAgD,CAAC,EAAE,OAAO,IAAI,EAAE;IACzE,MAAM,cAAc;QAChB,IAAI;QACJ,IAAI;QACJ,IAAI;IACR;IAEA,qBACI,8OAAC;QAAI,WAAU;kBACX,cAAA,8OAAC;YAAI,WAAW,CAAC,gEAAgE,EAAE,WAAW,CAAC,KAAK,EAAE;;;;;;;;;;;AAGlH", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/utils/baseUrl.ts"], "sourcesContent": ["export const API_URL = process.env.NEXT_PUBLIC_API_URL || 'https://localhost:7166'; "], "names": [], "mappings": ";;;AAAO,MAAM,UAAU,8DAAmC", "debugId": null}}, {"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/utils/tokenStorage.ts"], "sourcesContent": ["export const tokenStorage = {\r\n    getAccessToken: () => localStorage.getItem('accessToken'),\r\n    setAccessToken: (token: string) => localStorage.setItem('accessToken', token),\r\n    clearAccessToken: () => localStorage.removeItem('accessToken'),\r\n};"], "names": [], "mappings": ";;;AAAO,MAAM,eAAe;IACxB,gBAAgB,IAAM,aAAa,OAAO,CAAC;IAC3C,gBAAgB,CAAC,QAAkB,aAAa,OAAO,CAAC,eAAe;IACvE,kBAAkB,IAAM,aAAa,UAAU,CAAC;AACpD", "debugId": null}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/utils/interceptor.ts"], "sourcesContent": ["import { API_URL } from './baseUrl';\r\nimport { tokenStorage } from './tokenStorage';\r\nimport { redirect } from 'next/navigation';\r\n\r\ninterface CustomRequestInit extends RequestInit {\r\n    skipAuth?: boolean;\r\n}\r\n\r\nconst PUBLIC_ENDPOINTS = [\r\n    '/api/v1/auth/login',\r\n    '/api/v1/users',\r\n    '/api/v1/auth/forgot-password',\r\n    '/api/v1/auth/reset-password',\r\n    '/api/v1/auth/verify-email',\r\n];\r\n\r\nfunction isPublicEndpoint(url: string): boolean {\r\n    return PUBLIC_ENDPOINTS.some(endpoint => {\r\n        return url.includes(endpoint) || url.endsWith(endpoint);\r\n    });\r\n}\r\n\r\nlet refreshingPromise: Promise<boolean> | null = null;\r\n\r\nasync function refreshAccessToken(): Promise<boolean> {\r\n    const accessToken = tokenStorage.getAccessToken();\r\n    if (!accessToken) return false;\r\n\r\n    try {\r\n        const response = await fetch(`${API_URL}/api/v1/auth/refresh-token`, {\r\n            method: 'POST',\r\n            headers: {\r\n                'Authorization': `Bearer ${accessToken}`,\r\n                'Content-Type': 'application/json'\r\n            },\r\n        });\r\n\r\n        if (!response.ok) throw new Error('Refresh failed');\r\n\r\n        const data = await response.json();\r\n        tokenStorage.setAccessToken(data.data.accessToken);\r\n        return true;\r\n    } catch (error) {\r\n        return false;\r\n    } finally {\r\n        refreshingPromise = null;\r\n    }\r\n}\r\n\r\nexport const fetchInterceptor = async (url: string, options: CustomRequestInit = {}): Promise<Response> => {\r\n    const requestOptions: CustomRequestInit = {\r\n        ...options,\r\n    };\r\n\r\n    requestOptions.headers = {\r\n        'Content-Type': 'application/json',\r\n        ...requestOptions.headers,\r\n    };\r\n\r\n    const isPublic = options.skipAuth || isPublicEndpoint(url);\r\n\r\n    if (!isPublic) {\r\n        const token = tokenStorage.getAccessToken();\r\n        if (token) {\r\n            requestOptions.headers = {\r\n                ...requestOptions.headers,\r\n                Authorization: `Bearer ${token}`,\r\n            };\r\n        }\r\n    }\r\n\r\n    try {\r\n        let response = await fetch(url, requestOptions);\r\n\r\n        if (response.status === 401 && !requestOptions.skipAuth) {\r\n            if (!refreshingPromise) {\r\n                refreshingPromise = refreshAccessToken();\r\n            }\r\n            try {\r\n                await refreshingPromise;\r\n\r\n                requestOptions.headers = {\r\n                    ...requestOptions.headers,\r\n                    Authorization: `Bearer ${tokenStorage.getAccessToken()}`,\r\n                };\r\n\r\n                response = await fetch(url, requestOptions);\r\n            } catch (error) {\r\n                console.log('Token refresh failed:', error);\r\n                redirect('/login');\r\n            }\r\n        }\r\n\r\n        const responseData = await response.json();\r\n\r\n        if (!response.ok) {\r\n            throw new Error(responseData.message || `HTTP error! status: ${response.status}`);\r\n        }\r\n\r\n        return new Response(JSON.stringify(responseData), {\r\n            status: response.status,\r\n            statusText: response.statusText,\r\n            headers: response.headers\r\n        });\r\n\r\n    } catch (error) {\r\n        if (error instanceof Error) {\r\n            throw error;\r\n        }\r\n        throw new Error('Network error occurred');\r\n    }\r\n};"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAMA,MAAM,mBAAmB;IACrB;IACA;IACA;IACA;IACA;CACH;AAED,SAAS,iBAAiB,GAAW;IACjC,OAAO,iBAAiB,IAAI,CAAC,CAAA;QACzB,OAAO,IAAI,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC;IAClD;AACJ;AAEA,IAAI,oBAA6C;AAEjD,eAAe;IACX,MAAM,cAAc,4HAAA,CAAA,eAAY,CAAC,cAAc;IAC/C,IAAI,CAAC,aAAa,OAAO;IAEzB,IAAI;QACA,MAAM,WAAW,MAAM,MAAM,GAAG,uHAAA,CAAA,UAAO,CAAC,0BAA0B,CAAC,EAAE;YACjE,QAAQ;YACR,SAAS;gBACL,iBAAiB,CAAC,OAAO,EAAE,aAAa;gBACxC,gBAAgB;YACpB;QACJ;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;QAElC,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,4HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,KAAK,IAAI,CAAC,WAAW;QACjD,OAAO;IACX,EAAE,OAAO,OAAO;QACZ,OAAO;IACX,SAAU;QACN,oBAAoB;IACxB;AACJ;AAEO,MAAM,mBAAmB,OAAO,KAAa,UAA6B,CAAC,CAAC;IAC/E,MAAM,iBAAoC;QACtC,GAAG,OAAO;IACd;IAEA,eAAe,OAAO,GAAG;QACrB,gBAAgB;QAChB,GAAG,eAAe,OAAO;IAC7B;IAEA,MAAM,WAAW,QAAQ,QAAQ,IAAI,iBAAiB;IAEtD,IAAI,CAAC,UAAU;QACX,MAAM,QAAQ,4HAAA,CAAA,eAAY,CAAC,cAAc;QACzC,IAAI,OAAO;YACP,eAAe,OAAO,GAAG;gBACrB,GAAG,eAAe,OAAO;gBACzB,eAAe,CAAC,OAAO,EAAE,OAAO;YACpC;QACJ;IACJ;IAEA,IAAI;QACA,IAAI,WAAW,MAAM,MAAM,KAAK;QAEhC,IAAI,SAAS,MAAM,KAAK,OAAO,CAAC,eAAe,QAAQ,EAAE;YACrD,IAAI,CAAC,mBAAmB;gBACpB,oBAAoB;YACxB;YACA,IAAI;gBACA,MAAM;gBAEN,eAAe,OAAO,GAAG;oBACrB,GAAG,eAAe,OAAO;oBACzB,eAAe,CAAC,OAAO,EAAE,4HAAA,CAAA,eAAY,CAAC,cAAc,IAAI;gBAC5D;gBAEA,WAAW,MAAM,MAAM,KAAK;YAChC,EAAE,OAAO,OAAO;gBACZ,QAAQ,GAAG,CAAC,yBAAyB;gBACrC,CAAA,GAAA,kIAAA,CAAA,WAAQ,AAAD,EAAE;YACb;QACJ;QAEA,MAAM,eAAe,MAAM,SAAS,IAAI;QAExC,IAAI,CAAC,SAAS,EAAE,EAAE;YACd,MAAM,IAAI,MAAM,aAAa,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QACpF;QAEA,OAAO,IAAI,SAAS,KAAK,SAAS,CAAC,eAAe;YAC9C,QAAQ,SAAS,MAAM;YACvB,YAAY,SAAS,UAAU;YAC/B,SAAS,SAAS,OAAO;QAC7B;IAEJ,EAAE,OAAO,OAAO;QACZ,IAAI,iBAAiB,OAAO;YACxB,MAAM;QACV;QACA,MAAM,IAAI,MAAM;IACpB;AACJ", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/services/authService.ts"], "sourcesContent": ["import { SignInRequest, SignInResponse } from \"@/types/auth\";\r\nimport { API_URL } from \"@/utils/baseUrl\";\r\nimport { fetchInterceptor } from \"@/utils/interceptor\";\r\n\r\nexport const loginUser = async (request: SignInRequest): Promise<SignInResponse> => {\r\n    const response = await fetchInterceptor(\r\n        `${API_URL}/api/v1/auth/sign-in`,\r\n        {\r\n            method: 'POST',\r\n            body: JSON.stringify(request),\r\n        },\r\n    );\r\n\r\n    const data = await response.json();\r\n    const signInResponse = data.result as SignInResponse;\r\n    return signInResponse;\r\n};\r\n\r\nexport const SignInWithGoogle = async (code: string) => {\r\n    const response = await fetchInterceptor(`${API_URL}/api/v1/auth/outbound?code=${encodeURIComponent(code)}`, {\r\n        method: 'POST',\r\n        headers: {\r\n            'Accept': 'application/json',\r\n        },\r\n    });\r\n\r\n    if (!response.ok) {\r\n        throw new Error(`Lỗi HTTP: ${response.status} - ${response.statusText}`);\r\n    }\r\n    const data = await response.json();\r\n    return data;\r\n};\r\n\r\nexport const SignInWithFacebook = async (code: string) => {\r\n    const response = await fetch(`${API_URL}/api/v1/auth/facebook?code=${encodeURIComponent(code)}`, {\r\n        method: 'POST',\r\n        headers: {\r\n            'Accept': 'application/json',\r\n        },\r\n    });\r\n\r\n    if (!response.ok) {\r\n        throw new Error(`Lỗi HTTP: ${response.status} - ${response.statusText}`);\r\n    }\r\n    const data = await response.json();\r\n    return data;\r\n};"], "names": [], "mappings": ";;;;;AACA;AACA;;;AAEO,MAAM,YAAY,OAAO;IAC5B,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,mBAAgB,AAAD,EAClC,GAAG,uHAAA,CAAA,UAAO,CAAC,oBAAoB,CAAC,EAChC;QACI,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACzB;IAGJ,MAAM,OAAO,MAAM,SAAS,IAAI;IAChC,MAAM,iBAAiB,KAAK,MAAM;IAClC,OAAO;AACX;AAEO,MAAM,mBAAmB,OAAO;IACnC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,mBAAgB,AAAD,EAAE,GAAG,uHAAA,CAAA,UAAO,CAAC,2BAA2B,EAAE,mBAAmB,OAAO,EAAE;QACxG,QAAQ;QACR,SAAS;YACL,UAAU;QACd;IACJ;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QACd,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,SAAS,UAAU,EAAE;IAC3E;IACA,MAAM,OAAO,MAAM,SAAS,IAAI;IAChC,OAAO;AACX;AAEO,MAAM,qBAAqB,OAAO;IACrC,MAAM,WAAW,MAAM,MAAM,GAAG,uHAAA,CAAA,UAAO,CAAC,2BAA2B,EAAE,mBAAmB,OAAO,EAAE;QAC7F,QAAQ;QACR,SAAS;YACL,UAAU;QACd;IACJ;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QACd,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,SAAS,UAAU,EAAE;IAC3E;IACA,MAAM,OAAO,MAAM,SAAS,IAAI;IAChC,OAAO;AACX", "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/app/oauth2/callback/google/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { LoadingSpinner } from '@/components/ui/LoadingSpinner';\r\nimport { SignInWithGoogle } from '@/services/authService';\r\nimport { useRouter, useSearchParams } from 'next/navigation';\r\nimport { Suspense, useEffect, useState, useCallback } from 'react';\r\nimport toast from 'react-hot-toast';\r\nimport { useAuth } from '@/hooks/useAuth';\r\n\r\nfunction CallbackGoogleContent() {\r\n    const router = useRouter();\r\n    const searchParams = useSearchParams();\r\n    const code = searchParams.get('code');\r\n    const [isLoading, setIsLoading] = useState(true);\r\n    const [error, setError] = useState<string | null>(null);\r\n    const { login: authLogin } = useAuth();\r\n\r\n    const handleSendCode = useCallback(async (code: string) => {\r\n        try {\r\n            const data = await SignInWithGoogle(code);\r\n            if (data.result && data.result.accessToken) {\r\n                localStorage.setItem('accessToken', data.result.accessToken);\r\n                localStorage.setItem('refreshToken', data.result.refreshToken);\r\n                authLogin(data.result);\r\n                toast.success('Đăng nhập Google thành công! 🎉', {\r\n                    duration: 3000,\r\n                    position: 'top-right',\r\n                });\r\n                router.push('/');\r\n            } else {\r\n                setError('Không thể xử lý xác thực Google. Vui lòng kiểm tra phản hồi: ' + JSON.stringify(data));\r\n                toast.error('Không thể xử lý xác thực Google', {\r\n                    duration: 5000,\r\n                    position: 'top-right',\r\n                });\r\n            }\r\n        } catch (error: unknown) {\r\n            setError(`Lỗi trong quá trình xác thực Google: ${error instanceof Error ? error.message : 'Lỗi không xác định'}`);\r\n            toast.error(`Lỗi trong quá trình xác thực: ${error instanceof Error ? error.message : 'Lỗi không xác định'}`, {\r\n                duration: 5000,\r\n                position: 'top-right',\r\n            });\r\n        } finally {\r\n            setIsLoading(false);\r\n        }\r\n    }, [router]);\r\n\r\n    useEffect(() => {\r\n        if (code) {\r\n            handleSendCode(code);\r\n        } else {\r\n            setError('Không tìm thấy mã xác thực trong URL');\r\n            setIsLoading(false);\r\n            console.error('Không tìm thấy mã xác thực trong URL');\r\n            toast.error('Không tìm thấy mã xác thực trong URL', {\r\n                duration: 5000,\r\n                position: 'top-right',\r\n            });\r\n        }\r\n    }, [code, router, handleSendCode]);\r\n\r\n    if (isLoading) {\r\n        return <LoadingSpinner />;\r\n    }\r\n\r\n    return (\r\n        <div className=\"flex flex-col items-center justify-center min-h-screen\">\r\n            <h1 className=\"text-2xl font-bold mb-4\">Callback Google</h1>\r\n            {error ? (\r\n                <div className=\"text-red-600 mb-4\">{error}</div>\r\n            ) : (\r\n                <p className=\"text-gray-600\">Đang chuyển hướng về ứng dụng...</p>\r\n            )}\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default function CallbackGoogle() {\r\n    return (\r\n        <Suspense fallback={<LoadingSpinner />}>\r\n            <CallbackGoogleContent />\r\n        </Suspense>\r\n    );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASA,SAAS;IACL,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,OAAO,aAAa,GAAG,CAAC;IAC9B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,EAAE,OAAO,SAAS,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IAEnC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACtC,IAAI;YACA,MAAM,OAAO,MAAM,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE;YACpC,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,WAAW,EAAE;gBACxC,aAAa,OAAO,CAAC,eAAe,KAAK,MAAM,CAAC,WAAW;gBAC3D,aAAa,OAAO,CAAC,gBAAgB,KAAK,MAAM,CAAC,YAAY;gBAC7D,UAAU,KAAK,MAAM;gBACrB,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC,mCAAmC;oBAC7C,UAAU;oBACV,UAAU;gBACd;gBACA,OAAO,IAAI,CAAC;YAChB,OAAO;gBACH,SAAS,kEAAkE,KAAK,SAAS,CAAC;gBAC1F,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,mCAAmC;oBAC3C,UAAU;oBACV,UAAU;gBACd;YACJ;QACJ,EAAE,OAAO,OAAgB;YACrB,SAAS,CAAC,qCAAqC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,sBAAsB;YAChH,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,8BAA8B,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,sBAAsB,EAAE;gBAC1G,UAAU;gBACV,UAAU;YACd;QACJ,SAAU;YACN,aAAa;QACjB;IACJ,GAAG;QAAC;KAAO;IAEX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,MAAM;YACN,eAAe;QACnB,OAAO;YACH,SAAS;YACT,aAAa;YACb,QAAQ,KAAK,CAAC;YACd,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,wCAAwC;gBAChD,UAAU;gBACV,UAAU;YACd;QACJ;IACJ,GAAG;QAAC;QAAM;QAAQ;KAAe;IAEjC,IAAI,WAAW;QACX,qBAAO,8OAAC,0IAAA,CAAA,iBAAc;;;;;IAC1B;IAEA,qBACI,8OAAC;QAAI,WAAU;;0BACX,8OAAC;gBAAG,WAAU;0BAA0B;;;;;;YACvC,sBACG,8OAAC;gBAAI,WAAU;0BAAqB;;;;;qCAEpC,8OAAC;gBAAE,WAAU;0BAAgB;;;;;;;;;;;;AAI7C;AAEe,SAAS;IACpB,qBACI,8OAAC,qMAAA,CAAA,WAAQ;QAAC,wBAAU,8OAAC,0IAAA,CAAA,iBAAc;;;;;kBAC/B,cAAA,8OAAC;;;;;;;;;;AAGb", "debugId": null}}]}