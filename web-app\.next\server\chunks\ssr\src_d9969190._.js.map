{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/manager/doctors/DoctorTable.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Edit, Trash2, Eye,AppWindow } from 'lucide-react';\r\nimport { DoctorDetailResponse } from '@/types/doctor';\r\n\r\n\r\ninterface DoctorTableProps {\r\n    doctors: DoctorDetailResponse[];\r\n    onView: (doctor: DoctorDetailResponse) => void;\r\n    onEdit: (doctor: DoctorDetailResponse) => void;\r\n    onDelete: (id: number) => void;\r\n    onSchedule: (doctor: DoctorDetailResponse) => void;\r\n}\r\n\r\nexport const DoctorTable = ({ doctors, onView, onEdit, onDelete,onSchedule }: DoctorTableProps) => {\r\n    const getStatusColor = (isAvailable: boolean) => {\r\n        return isAvailable ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';\r\n    };\r\n\r\n    const getStatusText = (isAvailable: boolean) => {\r\n        return isAvailable ? 'Có sẵn' : 'Nghỉ';\r\n    };\r\n\r\n    return (\r\n        <div className=\"bg-white rounded-lg shadow-sm border overflow-hidden\">\r\n            <div className=\"overflow-x-auto\">\r\n                <table className=\"min-w-full divide-y divide-gray-200\">\r\n                    <thead className=\"bg-gray-50\">\r\n                        <tr>\r\n                            <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                Bác sĩ\r\n                            </th>\r\n                            <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                Khoa\r\n                            </th>\r\n                            <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                Email\r\n                            </th>\r\n                            <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                Trạng thái\r\n                            </th>\r\n                            <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                Thao tác\r\n                            </th>\r\n                        </tr>\r\n                    </thead>\r\n                    <tbody className=\"bg-white divide-y divide-gray-200\">\r\n                        {doctors.map((doctor) => (\r\n                            <tr key={doctor.doctorId} className=\"hover:bg-gray-50\">\r\n                                <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                    <div className=\"flex items-center\">\r\n                                        <div className=\"h-10 w-10 flex-shrink-0\">\r\n                                            <img\r\n                                                className=\"h-10 w-10 rounded-full object-cover\"\r\n                                                src={doctor.userAvatar || '/default-avatar.png'}\r\n                                                alt={doctor.fullName}\r\n                                            />\r\n                                        </div>\r\n                                        <div className=\"ml-4\">\r\n                                            <div className=\"text-sm font-medium text-gray-900\">{doctor.fullName}</div>\r\n                                            <div className=\"text-sm text-gray-500\">ID: {doctor.doctorId}</div>\r\n                                        </div>\r\n                                    </div>\r\n                                </td>\r\n                                <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                    <div className=\"text-sm text-gray-900\">{doctor.specialty.specialtyName || 'Không rõ'}</div>\r\n                                </td>\r\n                                <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                    <div className=\"text-sm text-gray-900\">{doctor.email}</div>\r\n                                </td>\r\n                                <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(doctor.isAvailable)}`}>\r\n                                        {getStatusText(doctor.isAvailable)}\r\n                                    </span>\r\n                                </td>\r\n                                <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\r\n                                    <div className=\"flex space-x-2\">\r\n                                        <button\r\n                                            onClick={() => onView(doctor)}\r\n                                            className=\"text-blue-600 hover:text-blue-900\"\r\n                                        >\r\n                                            <Eye className=\"w-4 h-4\" />\r\n                                        </button>\r\n                                        <button\r\n                                            onClick={() => onEdit(doctor)}\r\n                                            className=\"text-green-600 hover:text-green-900\"\r\n                                        >\r\n                                            <Edit className=\"w-4 h-4\" />\r\n                                        </button>\r\n                                        <button\r\n                                            onClick={() => onDelete(doctor.doctorId)}\r\n                                            className=\"text-red-600 hover:text-red-900\"\r\n                                        >\r\n                                            <Trash2 className=\"w-4 h-4\" />\r\n                                        </button>\r\n                                        <button\r\n                                            onClick={() => onSchedule(doctor)}\r\n                                            className=\"text-green-600 hover:text-green-900\"\r\n                                        >\r\n                                            <AppWindow className=\"w-4 h-4\" />\r\n                                        </button>\r\n                                    </div>\r\n                                </td>\r\n                            </tr>\r\n                        ))}\r\n                    </tbody>\r\n                </table>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAFA;;;AAcO,MAAM,cAAc,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAC,UAAU,EAAoB;IAC1F,MAAM,iBAAiB,CAAC;QACpB,OAAO,cAAc,gCAAgC;IACzD;IAEA,MAAM,gBAAgB,CAAC;QACnB,OAAO,cAAc,WAAW;IACpC;IAEA,qBACI,8OAAC;QAAI,WAAU;kBACX,cAAA,8OAAC;YAAI,WAAU;sBACX,cAAA,8OAAC;gBAAM,WAAU;;kCACb,8OAAC;wBAAM,WAAU;kCACb,cAAA,8OAAC;;8CACG,8OAAC;oCAAG,WAAU;8CAAiF;;;;;;8CAG/F,8OAAC;oCAAG,WAAU;8CAAiF;;;;;;8CAG/F,8OAAC;oCAAG,WAAU;8CAAiF;;;;;;8CAG/F,8OAAC;oCAAG,WAAU;8CAAiF;;;;;;8CAG/F,8OAAC;oCAAG,WAAU;8CAAiF;;;;;;;;;;;;;;;;;kCAKvG,8OAAC;wBAAM,WAAU;kCACZ,QAAQ,GAAG,CAAC,CAAC,uBACV,8OAAC;gCAAyB,WAAU;;kDAChC,8OAAC;wCAAG,WAAU;kDACV,cAAA,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;oDAAI,WAAU;8DACX,cAAA,8OAAC;wDACG,WAAU;wDACV,KAAK,OAAO,UAAU,IAAI;wDAC1B,KAAK,OAAO,QAAQ;;;;;;;;;;;8DAG5B,8OAAC;oDAAI,WAAU;;sEACX,8OAAC;4DAAI,WAAU;sEAAqC,OAAO,QAAQ;;;;;;sEACnE,8OAAC;4DAAI,WAAU;;gEAAwB;gEAAK,OAAO,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;kDAIvE,8OAAC;wCAAG,WAAU;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDAAyB,OAAO,SAAS,CAAC,aAAa,IAAI;;;;;;;;;;;kDAE9E,8OAAC;wCAAG,WAAU;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDAAyB,OAAO,KAAK;;;;;;;;;;;kDAExD,8OAAC;wCAAG,WAAU;kDACV,cAAA,8OAAC;4CAAK,WAAW,CAAC,yDAAyD,EAAE,eAAe,OAAO,WAAW,GAAG;sDAC5G,cAAc,OAAO,WAAW;;;;;;;;;;;kDAGzC,8OAAC;wCAAG,WAAU;kDACV,cAAA,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;oDACG,SAAS,IAAM,OAAO;oDACtB,WAAU;8DAEV,cAAA,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;8DAEnB,8OAAC;oDACG,SAAS,IAAM,OAAO;oDACtB,WAAU;8DAEV,cAAA,8OAAC,2MAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAEpB,8OAAC;oDACG,SAAS,IAAM,SAAS,OAAO,QAAQ;oDACvC,WAAU;8DAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEtB,8OAAC;oDACG,SAAS,IAAM,WAAW;oDAC1B,WAAU;8DAEV,cAAA,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+BAnD5B,OAAO,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;AA8DpD", "debugId": null}}, {"offset": {"line": 307, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/utils/baseUrl.ts"], "sourcesContent": ["export const API_URL = process.env.NEXT_PUBLIC_API_URL || 'https://localhost:7166';\r\n"], "names": [], "mappings": ";;;AAAO,MAAM,UAAU,8DAAmC", "debugId": null}}, {"offset": {"line": 317, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/utils/tokenStorage.ts"], "sourcesContent": ["export const tokenStorage = {\r\n    getAccessToken: () => localStorage.getItem('accessToken'),\r\n    setAccessToken: (token: string) => localStorage.setItem('accessToken', token),\r\n    clearAccessToken: () => localStorage.removeItem('accessToken'),\r\n};"], "names": [], "mappings": ";;;AAAO,MAAM,eAAe;IACxB,gBAAgB,IAAM,aAAa,OAAO,CAAC;IAC3C,gBAAgB,CAAC,QAAkB,aAAa,OAAO,CAAC,eAAe;IACvE,kBAAkB,IAAM,aAAa,UAAU,CAAC;AACpD", "debugId": null}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/utils/interceptor.ts"], "sourcesContent": ["import { API_URL } from './baseUrl';\r\nimport { tokenStorage } from './tokenStorage';\r\nimport { redirect } from 'next/navigation';\r\n\r\ninterface CustomRequestInit extends RequestInit {\r\n    skipAuth?: boolean;\r\n}\r\n\r\nconst PUBLIC_ENDPOINTS = [\r\n    '/api/v1/auth/login',\r\n    '/api/v1/users',\r\n    '/api/v1/auth/forgot-password',\r\n    '/api/v1/auth/reset-password',\r\n    '/api/v1/auth/verify-email',\r\n];\r\n\r\nfunction isPublicEndpoint(url: string): boolean {\r\n    return PUBLIC_ENDPOINTS.some(endpoint => {\r\n        return url.includes(endpoint) || url.endsWith(endpoint);\r\n    });\r\n}\r\n\r\nlet refreshingPromise: Promise<boolean> | null = null;\r\n\r\nasync function refreshAccessToken(): Promise<boolean> {\r\n    const accessToken = tokenStorage.getAccessToken();\r\n    if (!accessToken) return false;\r\n\r\n    try {\r\n        const response = await fetch(`${API_URL}/api/v1/auth/refresh-token`, {\r\n            method: 'POST',\r\n            headers: {\r\n                'Authorization': `Bearer ${accessToken}`,\r\n                'Content-Type': 'application/json'\r\n            },\r\n            // credentials: 'include'\r\n        });\r\n\r\n        if (!response.ok) throw new Error('Refresh failed');\r\n\r\n        const data = await response.json();\r\n        tokenStorage.setAccessToken(data.data.accessToken);\r\n        return true;\r\n    } catch (error) {\r\n        return false;\r\n    } finally {\r\n        refreshingPromise = null;\r\n    }\r\n}\r\n\r\nexport const fetchInterceptor = async (url: string, options: CustomRequestInit = {}): Promise<Response> => {\r\n    const requestOptions: CustomRequestInit = {\r\n        ...options,\r\n        // credentials: 'include'\r\n    };\r\n\r\n    requestOptions.headers = {\r\n        'Content-Type': 'application/json',\r\n        ...requestOptions.headers,\r\n    };\r\n\r\n    const isPublic = options.skipAuth || isPublicEndpoint(url);\r\n\r\n    if (!isPublic) {\r\n        const token = tokenStorage.getAccessToken();\r\n        if (token) {\r\n            requestOptions.headers = {\r\n                ...requestOptions.headers,\r\n                Authorization: `Bearer ${token}`,\r\n            };\r\n        }\r\n    }\r\n\r\n    try {\r\n        let response = await fetch(url, requestOptions);\r\n\r\n        if (response.status === 401 && !requestOptions.skipAuth) {\r\n            if (!refreshingPromise) {\r\n                refreshingPromise = refreshAccessToken();\r\n            }\r\n            try {\r\n                await refreshingPromise;\r\n\r\n                requestOptions.headers = {\r\n                    ...requestOptions.headers,\r\n                    Authorization: `Bearer ${tokenStorage.getAccessToken()}`,\r\n                };\r\n\r\n                response = await fetch(url, requestOptions);\r\n            } catch (error) {\r\n                console.log('Token refresh failed:', error);\r\n                redirect('/login');\r\n            }\r\n        }\r\n\r\n        if (!response.ok) {\r\n            const errorData = await response.json().catch(() => ({}));\r\n            throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\r\n        }\r\n\r\n        return response;\r\n\r\n    } catch (error) {\r\n        if (error instanceof Error) {\r\n            throw error;\r\n        }\r\n        throw new Error('Network error occurred');\r\n    }\r\n};"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAMA,MAAM,mBAAmB;IACrB;IACA;IACA;IACA;IACA;CACH;AAED,SAAS,iBAAiB,GAAW;IACjC,OAAO,iBAAiB,IAAI,CAAC,CAAA;QACzB,OAAO,IAAI,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC;IAClD;AACJ;AAEA,IAAI,oBAA6C;AAEjD,eAAe;IACX,MAAM,cAAc,4HAAA,CAAA,eAAY,CAAC,cAAc;IAC/C,IAAI,CAAC,aAAa,OAAO;IAEzB,IAAI;QACA,MAAM,WAAW,MAAM,MAAM,GAAG,uHAAA,CAAA,UAAO,CAAC,0BAA0B,CAAC,EAAE;YACjE,QAAQ;YACR,SAAS;gBACL,iBAAiB,CAAC,OAAO,EAAE,aAAa;gBACxC,gBAAgB;YACpB;QAEJ;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;QAElC,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,4HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,KAAK,IAAI,CAAC,WAAW;QACjD,OAAO;IACX,EAAE,OAAO,OAAO;QACZ,OAAO;IACX,SAAU;QACN,oBAAoB;IACxB;AACJ;AAEO,MAAM,mBAAmB,OAAO,KAAa,UAA6B,CAAC,CAAC;IAC/E,MAAM,iBAAoC;QACtC,GAAG,OAAO;IAEd;IAEA,eAAe,OAAO,GAAG;QACrB,gBAAgB;QAChB,GAAG,eAAe,OAAO;IAC7B;IAEA,MAAM,WAAW,QAAQ,QAAQ,IAAI,iBAAiB;IAEtD,IAAI,CAAC,UAAU;QACX,MAAM,QAAQ,4HAAA,CAAA,eAAY,CAAC,cAAc;QACzC,IAAI,OAAO;YACP,eAAe,OAAO,GAAG;gBACrB,GAAG,eAAe,OAAO;gBACzB,eAAe,CAAC,OAAO,EAAE,OAAO;YACpC;QACJ;IACJ;IAEA,IAAI;QACA,IAAI,WAAW,MAAM,MAAM,KAAK;QAEhC,IAAI,SAAS,MAAM,KAAK,OAAO,CAAC,eAAe,QAAQ,EAAE;YACrD,IAAI,CAAC,mBAAmB;gBACpB,oBAAoB;YACxB;YACA,IAAI;gBACA,MAAM;gBAEN,eAAe,OAAO,GAAG;oBACrB,GAAG,eAAe,OAAO;oBACzB,eAAe,CAAC,OAAO,EAAE,4HAAA,CAAA,eAAY,CAAC,cAAc,IAAI;gBAC5D;gBAEA,WAAW,MAAM,MAAM,KAAK;YAChC,EAAE,OAAO,OAAO;gBACZ,QAAQ,GAAG,CAAC,yBAAyB;gBACrC,CAAA,GAAA,kIAAA,CAAA,WAAQ,AAAD,EAAE;YACb;QACJ;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YACd,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;YACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QACjF;QAEA,OAAO;IAEX,EAAE,OAAO,OAAO;QACZ,IAAI,iBAAiB,OAAO;YACxB,MAAM;QACV;QACA,MAAM,IAAI,MAAM;IACpB;AACJ", "debugId": null}}, {"offset": {"line": 428, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/services/doctorService.ts"], "sourcesContent": ["import { ApiResponse } from \"@/types/apiResonse\";\r\nimport { <PERSON><PERSON><PERSON><PERSON>Request, DoctorCreationR<PERSON>ponse, Doctor<PERSON><PERSON>il<PERSON><PERSON><PERSON><PERSON>, DoctorSearchResponse, Gender } from \"@/types/doctor\";\r\nimport { PageResponse } from \"@/types/pageResponse\";\r\nimport { API_URL } from \"@/utils/baseUrl\";\r\nimport { fetchInterceptor } from \"@/utils/interceptor\";\r\n\r\nexport interface SearchDoctorsParams {\r\n    doctorName?: string;\r\n    specialtyName?: string;\r\n    gender?: Gender;\r\n    isAvailable?: boolean;\r\n    orderBy?: string;\r\n    page?: number;\r\n    pageSize?: number;\r\n}\r\n\r\nconst accessToken = localStorage.getItem('accessToken');\r\n\r\nexport const doctorService = {\r\n    async searchDoctors(params: SearchDoctorsParams = {}): Promise<ApiResponse<PageResponse<DoctorSearchResponse>>> {\r\n        const queryParams = new URLSearchParams();\r\n\r\n        if (params.doctorName) queryParams.append('doctorName', params.doctorName);\r\n        if (params.specialtyName) queryParams.append('specialtyName', params.specialtyName);\r\n        if (params.gender) queryParams.append('gender', params.gender);\r\n        if (params.isAvailable !== undefined) queryParams.append('isAvailable', params.isAvailable.toString());\r\n        if (params.orderBy) queryParams.append('orderBy', params.orderBy);\r\n        if (params.page) queryParams.append('page', params.page.toString());\r\n        if (params.pageSize) queryParams.append('pageSize', params.pageSize.toString());\r\n\r\n        const url = `${API_URL}/api/v1/doctors/search?${queryParams.toString()}`;\r\n        const response = await fetch(url, {\r\n            method: 'GET',\r\n            headers: {\r\n                'Content-Type': 'application/json',\r\n            },\r\n        });\r\n\r\n        console.log('🔍 Debug - Response Status:', response.status);\r\n        console.log('🔍 Debug - Response OK:', response.ok);\r\n\r\n        if (!response.ok) {\r\n            throw new Error(`HTTP error! status: ${response.status}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        console.log('🔍 Debug - Response Data:', data);\r\n        return data;\r\n    },\r\n\r\n    async getDoctorDetails(doctorId: number): Promise<ApiResponse<DoctorDetailResponse>> {\r\n        const url = `${API_URL}/api/v1/doctors/${doctorId}`;\r\n\r\n        const response = await fetch(url, {\r\n            method: 'GET',\r\n            headers: {\r\n                'Content-Type': 'application/json',\r\n            },\r\n        });\r\n\r\n        console.log('🔍 Debug - Doctor Details Response Status:', response.status);\r\n\r\n        if (!response.ok) {\r\n            throw new Error(`HTTP error! status: ${response.status}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        console.log('🔍 Debug - Doctor Details Response Data:', data);\r\n        return data;\r\n    },\r\n\r\n    async getDoctorAppointmentSchedule(doctorId: number, fromDate?: string, toDate?: string) {\r\n        const params = new URLSearchParams();\r\n        if (fromDate) params.append('fromDate', fromDate);\r\n        if (toDate) params.append('toDate', toDate);\r\n        const url = `${API_URL}/api/v1/doctors/${doctorId}/schedule?${params.toString()}`;\r\n        const response = await fetch(url, {\r\n            method: 'GET',\r\n            headers: {\r\n                'Content-Type': 'application/json',\r\n            },\r\n        });\r\n        if (!response.ok) {\r\n            throw new Error(`HTTP error! status: ${response.status}`);\r\n        }\r\n        return response.json();\r\n    },\r\n\r\n    async getDoctorWorkingSchedule(doctorId: number, daysAhead: number = 14) {\r\n        const params = new URLSearchParams();\r\n        params.append('daysAhead', daysAhead.toString());\r\n        const url = `${API_URL}/api/v1/doctors/${doctorId}/working-schedule?${params.toString()}`;\r\n        const response = await fetch(url, {\r\n            method: 'GET',\r\n            headers: {\r\n                'Content-Type': 'application/json',\r\n            },\r\n        });\r\n        if (!response.ok) {\r\n            throw new Error(`HTTP error! status: ${response.status}`);\r\n        }\r\n        return response.json();\r\n    }\r\n};\r\n\r\nexport const getDoctors = async (params: SearchDoctorsParams = {}): Promise<ApiResponse<PageResponse<DoctorDetailResponse>>> => {\r\n    const queryParams = new URLSearchParams();\r\n\r\n    if (params.doctorName) queryParams.append('doctorName', params.doctorName);\r\n    if (params.specialtyName) queryParams.append('specialtyName', params.specialtyName);\r\n    if (params.gender) queryParams.append('gender', params.gender);\r\n    if (params.isAvailable !== undefined) queryParams.append('isAvailable', params.isAvailable.toString());\r\n    if (params.orderBy) queryParams.append('orderBy', params.orderBy);\r\n    if (params.page) queryParams.append('page', params.page.toString());\r\n    if (params.pageSize) queryParams.append('pageSize', params.pageSize.toString());\r\n\r\n    const url = `${API_URL}/api/v1/doctors?${queryParams.toString()}`;\r\n    console.log('🔍 Debug - Search Doctors URL:', url);\r\n    console.log('🔍 Debug - Search Parameters:', params);\r\n\r\n    const response = await fetch(url, {\r\n        method: 'GET',\r\n        headers: {\r\n            'Content-Type': 'application/json',\r\n            ...(accessToken && { 'Authorization': `Bearer ${accessToken}` }),\r\n        },\r\n    });\r\n\r\n    console.log('🔍 Debug - Response Status:', response.status);\r\n    console.log('🔍 Debug - Response OK:', response.ok);\r\n\r\n    if (!response.ok) {\r\n        throw new Error(`HTTP error! status: ${response.status}`);\r\n    }\r\n\r\n    const data = await response.json();\r\n    console.log('🔍 Debug - Response Data:', data);\r\n    return data;\r\n};\r\n\r\n\r\nexport const createDoctor = async (data: DoctorCreationRequest): Promise<ApiResponse<DoctorCreationResponse>> => {\r\n    const response = await fetchInterceptor(`${API_URL}/api/v1/doctors`, {\r\n        method: \"POST\",\r\n        body: JSON.stringify(data)\r\n    });\r\n\r\n    const result: ApiResponse<DoctorCreationResponse> = await response.json();\r\n    return result;\r\n};\r\n\r\nexport const getDoctorWorkingSlots = async (doctorId: number, workDate?: string): Promise<ApiResponse<object>> => {\r\n    const params = new URLSearchParams();\r\n    if (workDate) params.append('fromDate', workDate);\r\n    const url = `${API_URL}/api/v1/doctors/${doctorId}/slot-day?${params.toString()}`;\r\n    const response = await fetch(url, {\r\n        method: 'GET',\r\n        headers: {\r\n            'Content-Type': 'application/json',\r\n        },\r\n    });\r\n    if (!response.ok) {\r\n        throw new Error(`HTTP error! status: ${response.status}`);\r\n    }\r\n    const result: ApiResponse<object> = await response.json();\r\n    return result;\r\n} \r\n"], "names": [], "mappings": ";;;;;;AAGA;AACA;;;AAYA,MAAM,cAAc,aAAa,OAAO,CAAC;AAElC,MAAM,gBAAgB;IACzB,MAAM,eAAc,SAA8B,CAAC,CAAC;QAChD,MAAM,cAAc,IAAI;QAExB,IAAI,OAAO,UAAU,EAAE,YAAY,MAAM,CAAC,cAAc,OAAO,UAAU;QACzE,IAAI,OAAO,aAAa,EAAE,YAAY,MAAM,CAAC,iBAAiB,OAAO,aAAa;QAClF,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC7D,IAAI,OAAO,WAAW,KAAK,WAAW,YAAY,MAAM,CAAC,eAAe,OAAO,WAAW,CAAC,QAAQ;QACnG,IAAI,OAAO,OAAO,EAAE,YAAY,MAAM,CAAC,WAAW,OAAO,OAAO;QAChE,IAAI,OAAO,IAAI,EAAE,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAChE,IAAI,OAAO,QAAQ,EAAE,YAAY,MAAM,CAAC,YAAY,OAAO,QAAQ,CAAC,QAAQ;QAE5E,MAAM,MAAM,GAAG,uHAAA,CAAA,UAAO,CAAC,uBAAuB,EAAE,YAAY,QAAQ,IAAI;QACxE,MAAM,WAAW,MAAM,MAAM,KAAK;YAC9B,QAAQ;YACR,SAAS;gBACL,gBAAgB;YACpB;QACJ;QAEA,QAAQ,GAAG,CAAC,+BAA+B,SAAS,MAAM;QAC1D,QAAQ,GAAG,CAAC,2BAA2B,SAAS,EAAE;QAElD,IAAI,CAAC,SAAS,EAAE,EAAE;YACd,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC5D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,6BAA6B;QACzC,OAAO;IACX;IAEA,MAAM,kBAAiB,QAAgB;QACnC,MAAM,MAAM,GAAG,uHAAA,CAAA,UAAO,CAAC,gBAAgB,EAAE,UAAU;QAEnD,MAAM,WAAW,MAAM,MAAM,KAAK;YAC9B,QAAQ;YACR,SAAS;gBACL,gBAAgB;YACpB;QACJ;QAEA,QAAQ,GAAG,CAAC,8CAA8C,SAAS,MAAM;QAEzE,IAAI,CAAC,SAAS,EAAE,EAAE;YACd,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC5D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,4CAA4C;QACxD,OAAO;IACX;IAEA,MAAM,8BAA6B,QAAgB,EAAE,QAAiB,EAAE,MAAe;QACnF,MAAM,SAAS,IAAI;QACnB,IAAI,UAAU,OAAO,MAAM,CAAC,YAAY;QACxC,IAAI,QAAQ,OAAO,MAAM,CAAC,UAAU;QACpC,MAAM,MAAM,GAAG,uHAAA,CAAA,UAAO,CAAC,gBAAgB,EAAE,SAAS,UAAU,EAAE,OAAO,QAAQ,IAAI;QACjF,MAAM,WAAW,MAAM,MAAM,KAAK;YAC9B,QAAQ;YACR,SAAS;gBACL,gBAAgB;YACpB;QACJ;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YACd,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC5D;QACA,OAAO,SAAS,IAAI;IACxB;IAEA,MAAM,0BAAyB,QAAgB,EAAE,YAAoB,EAAE;QACnE,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,CAAC,aAAa,UAAU,QAAQ;QAC7C,MAAM,MAAM,GAAG,uHAAA,CAAA,UAAO,CAAC,gBAAgB,EAAE,SAAS,kBAAkB,EAAE,OAAO,QAAQ,IAAI;QACzF,MAAM,WAAW,MAAM,MAAM,KAAK;YAC9B,QAAQ;YACR,SAAS;gBACL,gBAAgB;YACpB;QACJ;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YACd,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC5D;QACA,OAAO,SAAS,IAAI;IACxB;AACJ;AAEO,MAAM,aAAa,OAAO,SAA8B,CAAC,CAAC;IAC7D,MAAM,cAAc,IAAI;IAExB,IAAI,OAAO,UAAU,EAAE,YAAY,MAAM,CAAC,cAAc,OAAO,UAAU;IACzE,IAAI,OAAO,aAAa,EAAE,YAAY,MAAM,CAAC,iBAAiB,OAAO,aAAa;IAClF,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;IAC7D,IAAI,OAAO,WAAW,KAAK,WAAW,YAAY,MAAM,CAAC,eAAe,OAAO,WAAW,CAAC,QAAQ;IACnG,IAAI,OAAO,OAAO,EAAE,YAAY,MAAM,CAAC,WAAW,OAAO,OAAO;IAChE,IAAI,OAAO,IAAI,EAAE,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;IAChE,IAAI,OAAO,QAAQ,EAAE,YAAY,MAAM,CAAC,YAAY,OAAO,QAAQ,CAAC,QAAQ;IAE5E,MAAM,MAAM,GAAG,uHAAA,CAAA,UAAO,CAAC,gBAAgB,EAAE,YAAY,QAAQ,IAAI;IACjE,QAAQ,GAAG,CAAC,kCAAkC;IAC9C,QAAQ,GAAG,CAAC,iCAAiC;IAE7C,MAAM,WAAW,MAAM,MAAM,KAAK;QAC9B,QAAQ;QACR,SAAS;YACL,gBAAgB;YAChB,GAAI,eAAe;gBAAE,iBAAiB,CAAC,OAAO,EAAE,aAAa;YAAC,CAAC;QACnE;IACJ;IAEA,QAAQ,GAAG,CAAC,+BAA+B,SAAS,MAAM;IAC1D,QAAQ,GAAG,CAAC,2BAA2B,SAAS,EAAE;IAElD,IAAI,CAAC,SAAS,EAAE,EAAE;QACd,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;IAC5D;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAChC,QAAQ,GAAG,CAAC,6BAA6B;IACzC,OAAO;AACX;AAGO,MAAM,eAAe,OAAO;IAC/B,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,mBAAgB,AAAD,EAAE,GAAG,uHAAA,CAAA,UAAO,CAAC,eAAe,CAAC,EAAE;QACjE,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACzB;IAEA,MAAM,SAA8C,MAAM,SAAS,IAAI;IACvE,OAAO;AACX;AAEO,MAAM,wBAAwB,OAAO,UAAkB;IAC1D,MAAM,SAAS,IAAI;IACnB,IAAI,UAAU,OAAO,MAAM,CAAC,YAAY;IACxC,MAAM,MAAM,GAAG,uHAAA,CAAA,UAAO,CAAC,gBAAgB,EAAE,SAAS,UAAU,EAAE,OAAO,QAAQ,IAAI;IACjF,MAAM,WAAW,MAAM,MAAM,KAAK;QAC9B,QAAQ;QACR,SAAS;YACL,gBAAgB;QACpB;IACJ;IACA,IAAI,CAAC,SAAS,EAAE,EAAE;QACd,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;IAC5D;IACA,MAAM,SAA8B,MAAM,SAAS,IAAI;IACvD,OAAO;AACX", "debugId": null}}, {"offset": {"line": 573, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/services/specialtyService.ts"], "sourcesContent": ["import { ApiResponse } from \"@/types/apiResonse\";\r\nimport { ErrorResponse } from \"@/types/errorResponse\";\r\nimport { SpecialtyCreationRequest, SpecialtyCreationResponse, SpecialtyDetailResponse } from \"@/types/specialty\";\r\nimport { PageResponse } from \"@/types/pageResponse\";\r\nimport { fetchInterceptor } from \"@/utils/interceptor\";\r\nimport { API_URL } from \"@/utils/baseUrl\";\r\n\r\n\r\nexport const createSpecialty = async (data: SpecialtyCreationRequest): Promise<ApiResponse<SpecialtyCreationResponse>> => {\r\n    const response = await fetchInterceptor(`${API_URL}/api/v1/specialty`, {\r\n        method: \"POST\",\r\n        body: JSON.stringify(data)\r\n    });\r\n\r\n    const result: ApiResponse<SpecialtyCreationResponse> = await response.json();\r\n    return result;\r\n};\r\n\r\nexport const getSpecialties = async (page: number = 1, size: number = 10, keyword: string = \"\"): Promise<ApiResponse<PageResponse<SpecialtyDetailResponse>>> => {\r\n    let url = `${API_URL}/api/v1/specialty?page=${page}&size=${size}`;\r\n    if (keyword.trim()) {\r\n        url += `&keyword=${encodeURIComponent(keyword)}`;\r\n    }\r\n\r\n    try {\r\n        const response = await fetchInterceptor(url, {\r\n            method: \"GET\"\r\n        });\r\n\r\n        const result: ApiResponse<PageResponse<SpecialtyDetailResponse>> = await response.json();\r\n        return result;\r\n    } catch (error) {\r\n        console.error('Error in getSpecialties:', error);\r\n        throw error;\r\n    }\r\n};\r\n\r\nexport const deleteSpecialty = async (id: number): Promise<ApiResponse<object>> => {\r\n    const response = await fetchInterceptor(`${API_URL}/api/v1/specialty/${id}`, {\r\n        method: \"DELETE\"\r\n    });\r\n\r\n    const result: ApiResponse<object> = await response.json();\r\n    return result;\r\n};\r\n"], "names": [], "mappings": ";;;;;AAIA;AACA;;;AAGO,MAAM,kBAAkB,OAAO;IAClC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,mBAAgB,AAAD,EAAE,GAAG,uHAAA,CAAA,UAAO,CAAC,iBAAiB,CAAC,EAAE;QACnE,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACzB;IAEA,MAAM,SAAiD,MAAM,SAAS,IAAI;IAC1E,OAAO;AACX;AAEO,MAAM,iBAAiB,OAAO,OAAe,CAAC,EAAE,OAAe,EAAE,EAAE,UAAkB,EAAE;IAC1F,IAAI,MAAM,GAAG,uHAAA,CAAA,UAAO,CAAC,uBAAuB,EAAE,KAAK,MAAM,EAAE,MAAM;IACjE,IAAI,QAAQ,IAAI,IAAI;QAChB,OAAO,CAAC,SAAS,EAAE,mBAAmB,UAAU;IACpD;IAEA,IAAI;QACA,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK;YACzC,QAAQ;QACZ;QAEA,MAAM,SAA6D,MAAM,SAAS,IAAI;QACtF,OAAO;IACX,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACV;AACJ;AAEO,MAAM,kBAAkB,OAAO;IAClC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,mBAAgB,AAAD,EAAE,GAAG,uHAAA,CAAA,UAAO,CAAC,kBAAkB,EAAE,IAAI,EAAE;QACzE,QAAQ;IACZ;IAEA,MAAM,SAA8B,MAAM,SAAS,IAAI;IACvD,OAAO;AACX", "debugId": null}}, {"offset": {"line": 619, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/manager/doctors/DoctorModal.tsx"], "sourcesContent": ["'use client'\r\nimport { useState, useEffect } from 'react';\r\nimport { Doctor<PERSON><PERSON>tionRequest, DoctorDetailResponse, Gender } from '@/types/doctor';\r\nimport { SpecialtyDetailResponse } from '@/types/specialty';\r\nimport { createDoctor } from '@/services/doctorService';\r\nimport { getSpecialties } from '@/services/specialtyService';\r\nimport toast from 'react-hot-toast';\r\n\r\ninterface DoctorModalProps {\r\n    isOpen: boolean;\r\n    doctor: DoctorDetailResponse | null;\r\n    onClose: () => void;\r\n    onSuccess: () => void;\r\n}\r\n\r\nexport const DoctorModal = ({ isOpen, doctor, onClose, onSuccess }: DoctorModalProps) => {\r\n    const [loading, setLoading] = useState(false);\r\n    const [loadingSpecialties, setLoadingSpecialties] = useState(false);\r\n    const [specialties, setSpecialties] = useState<SpecialtyDetailResponse[]>([]);\r\n    const [errors, setErrors] = useState<Record<string, string>>({});\r\n\r\n    const [formData, setFormData] = useState({\r\n        email: '',\r\n        phone: '',\r\n        firstName: '',\r\n        lastName: '',\r\n        specialtyId: '',\r\n        licenseNumber: '',\r\n        degree: '',\r\n        consultationFee: '',\r\n        gender: '',\r\n        yearsOfExperience: '',\r\n        bio: '',\r\n        avatar: '',\r\n        isAvailable: true\r\n    });\r\n\r\n    useEffect(() => {\r\n        if (isOpen) {\r\n            fetchSpecialties();\r\n            if (doctor) {\r\n            } else {\r\n                setFormData({\r\n                    email: '',\r\n                    phone: '',\r\n                    firstName: '',\r\n                    lastName: '',\r\n                    specialtyId: '',\r\n                    licenseNumber: '',\r\n                    degree: '',\r\n                    consultationFee: '',\r\n                    gender: '',\r\n                    yearsOfExperience: '',\r\n                    bio: '',\r\n                    avatar: '',\r\n                    isAvailable: true\r\n                });\r\n            }\r\n            setErrors({});\r\n        }\r\n    }, [isOpen, doctor]);\r\n\r\n    const fetchSpecialties = async () => {\r\n        setLoadingSpecialties(true);\r\n        try {\r\n            const response = await getSpecialties(1, 100);\r\n            if (response.code === 200 && response.result) {\r\n                setSpecialties(response.result.items || []);\r\n            } else {\r\n                toast.error('Không thể tải danh sách chuyên khoa', {\r\n                    duration: 3000,\r\n                    style: {\r\n                        background: '#DC2626',\r\n                        color: '#fff',\r\n                    }\r\n                });\r\n            }\r\n        } catch (error) {\r\n            toast.error(`Lỗi khi tải danh sách chuyên khoa: ${error instanceof Error ? error.message : 'Unknown error'}`, {\r\n                duration: 3000,\r\n                style: {\r\n                    background: '#DC2626',\r\n                    color: '#fff',\r\n                }\r\n            });\r\n        } finally {\r\n            setLoadingSpecialties(false);\r\n        }\r\n    };\r\n\r\n    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\r\n        const { name, value, type } = e.target;\r\n        setFormData(prev => ({\r\n            ...prev,\r\n            [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value\r\n        }));\r\n\r\n        if (errors[name]) {\r\n            setErrors(prev => ({ ...prev, [name]: '' }));\r\n        }\r\n    };\r\n\r\n    const validateForm = (): boolean => {\r\n        const newErrors: Record<string, string> = {};\r\n        if (!formData.email.trim()) {\r\n            newErrors.email = 'Email là bắt buộc';\r\n        } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.email)) {\r\n            newErrors.email = 'Email không đúng định dạng';\r\n        }\r\n        if (!formData.phone.trim()) {\r\n            newErrors.phone = 'Số điện thoại là bắt buộc';\r\n        } else if (!/^[0-9]{10,11}$/.test(formData.phone)) {\r\n            newErrors.phone = 'Số điện thoại phải có 10-11 chữ số';\r\n        }\r\n        if (!formData.firstName.trim()) {\r\n            newErrors.firstName = 'Tên là bắt buộc';\r\n        }\r\n        if (!formData.lastName.trim()) {\r\n            newErrors.lastName = 'Họ là bắt buộc';\r\n        }\r\n        if (!formData.specialtyId.trim()) {\r\n            newErrors.specialtyId = 'Chuyên khoa là bắt buộc';\r\n        } else if (specialties.length === 0) {\r\n            newErrors.specialtyId = 'Không có chuyên khoa nào để chọn';\r\n        }\r\n        if (!formData.licenseNumber.trim()) {\r\n            newErrors.licenseNumber = 'Số giấy phép hành nghề là bắt buộc';\r\n        }\r\n        if (!formData.consultationFee.trim()) {\r\n            newErrors.consultationFee = 'Phí khám là bắt buộc';\r\n        } else if (isNaN(Number(formData.consultationFee)) || Number(formData.consultationFee) < 0) {\r\n            newErrors.consultationFee = 'Phí khám phải là số dương';\r\n        }\r\n        if (!formData.yearsOfExperience.trim()) {\r\n            newErrors.yearsOfExperience = 'Số năm kinh nghiệm là bắt buộc';\r\n        } else if (isNaN(Number(formData.yearsOfExperience)) || Number(formData.yearsOfExperience) < 0) {\r\n            newErrors.yearsOfExperience = 'Số năm kinh nghiệm phải là số dương';\r\n        }\r\n\r\n        setErrors(newErrors);\r\n        return Object.keys(newErrors).length === 0;\r\n    };\r\n\r\n    const handleSubmit = async (e: React.FormEvent) => {\r\n        e.preventDefault();\r\n\r\n        if (!validateForm()) {\r\n            return;\r\n        }\r\n\r\n        if (specialties.length === 0) {\r\n            toast.error('Không thể tạo bác sĩ khi chưa có chuyên khoa nào!', {\r\n                duration: 4000,\r\n                style: {\r\n                    background: '#DC2626',\r\n                    color: '#fff',\r\n                }\r\n            });\r\n            return;\r\n        }\r\n\r\n        setLoading(true);\r\n\r\n        try {\r\n            const request: DoctorCreationRequest = {\r\n                Email: formData.email,\r\n                Phone: formData.phone,\r\n                FirstName: formData.firstName,\r\n                LastName: formData.lastName,\r\n                SpecialtyId: Number(formData.specialtyId),\r\n                LicenseNumber: formData.licenseNumber,\r\n                Degree: formData.degree || undefined,\r\n                ConsultationFee: Number(formData.consultationFee),\r\n                Gender: formData.gender as Gender || undefined,\r\n                YearsOfExperience: Number(formData.yearsOfExperience),\r\n                Bio: formData.bio || undefined,\r\n                Avatar: formData.avatar || undefined\r\n            };\r\n\r\n            const response = await createDoctor(request);\r\n\r\n            if (response.code === 200 && response.result) {\r\n                toast.success('🎉 Thêm bác sĩ thành công!', {\r\n                    duration: 3000,\r\n                    style: {\r\n                        background: '#059669',\r\n                        color: '#fff',\r\n                    }\r\n                });\r\n                onSuccess();\r\n                onClose();\r\n            } else {\r\n                toast.error(`❌ ${response.message || 'Thêm bác sĩ thất bại. Vui lòng thử lại!'}`, {\r\n                    duration: 4000,\r\n                    style: {\r\n                        background: '#DC2626',\r\n                        color: '#fff',\r\n                    }\r\n                });\r\n            }\r\n        } catch (error: any) {\r\n            console.error('Error creating doctor:', error);\r\n\r\n            let errorMessage = 'Đã xảy ra lỗi khi tạo bác sĩ!';\r\n            if (error?.message) {\r\n                errorMessage = `❌ ${error.message}`;\r\n            }\r\n\r\n            toast.error(errorMessage, {\r\n                duration: 4000,\r\n                style: {\r\n                    background: '#DC2626',\r\n                    color: '#fff',\r\n                }\r\n            });\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    if (!isOpen) return null;\r\n\r\n    return (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n            <div className=\"bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto\">\r\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\r\n                    {doctor ? 'Chỉnh sửa Bác sĩ' : 'Thêm Bác sĩ mới'}\r\n                </h3>\r\n                <form onSubmit={handleSubmit} className=\"space-y-4\">\r\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                        {/* Tên */}\r\n                        <div>\r\n                            <label className=\"block text-sm font-medium text-gray-700\">\r\n                                Tên <span className=\"text-red-500\">*</span>\r\n                            </label>\r\n                            <input\r\n                                name=\"firstName\"\r\n                                type=\"text\"\r\n                                value={formData.firstName}\r\n                                onChange={handleInputChange}\r\n                                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.firstName ? 'border-red-500' : 'border-gray-300'\r\n                                    }`}\r\n                                placeholder=\"Nhập tên\"\r\n                            />\r\n                            {errors.firstName && <p className=\"mt-1 text-sm text-red-600\">{errors.firstName}</p>}\r\n                        </div>\r\n\r\n                        {/* Họ */}\r\n                        <div>\r\n                            <label className=\"block text-sm font-medium text-gray-700\">\r\n                                Họ <span className=\"text-red-500\">*</span>\r\n                            </label>\r\n                            <input\r\n                                name=\"lastName\"\r\n                                type=\"text\"\r\n                                value={formData.lastName}\r\n                                onChange={handleInputChange}\r\n                                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.lastName ? 'border-red-500' : 'border-gray-300'\r\n                                    }`}\r\n                                placeholder=\"Nhập họ\"\r\n                            />\r\n                            {errors.lastName && <p className=\"mt-1 text-sm text-red-600\">{errors.lastName}</p>}\r\n                        </div>\r\n\r\n                        {/* Email */}\r\n                        <div>\r\n                            <label className=\"block text-sm font-medium text-gray-700\">\r\n                                Email <span className=\"text-red-500\">*</span>\r\n                            </label>\r\n                            <input\r\n                                name=\"email\"\r\n                                type=\"email\"\r\n                                value={formData.email}\r\n                                onChange={handleInputChange}\r\n                                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.email ? 'border-red-500' : 'border-gray-300'\r\n                                    }`}\r\n                                placeholder=\"Nhập email\"\r\n                            />\r\n                            {errors.email && <p className=\"mt-1 text-sm text-red-600\">{errors.email}</p>}\r\n                        </div>\r\n\r\n                        {/* Số điện thoại */}\r\n                        <div>\r\n                            <label className=\"block text-sm font-medium text-gray-700\">\r\n                                Số điện thoại <span className=\"text-red-500\">*</span>\r\n                            </label>\r\n                            <input\r\n                                name=\"phone\"\r\n                                type=\"tel\"\r\n                                value={formData.phone}\r\n                                onChange={handleInputChange}\r\n                                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.phone ? 'border-red-500' : 'border-gray-300'\r\n                                    }`}\r\n                                placeholder=\"Nhập số điện thoại\"\r\n                            />\r\n                            {errors.phone && <p className=\"mt-1 text-sm text-red-600\">{errors.phone}</p>}\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                        {/* Chuyên khoa */}\r\n                        <div>\r\n                            <div className=\"flex items-center justify-between\">\r\n                                <label className=\"block text-sm font-medium text-gray-700\">\r\n                                    Chuyên khoa <span className=\"text-red-500\">*</span>\r\n                                </label>\r\n                                <button\r\n                                    type=\"button\"\r\n                                    onClick={fetchSpecialties}\r\n                                    disabled={loadingSpecialties}\r\n                                    className=\"text-sm text-blue-600 hover:text-blue-800 disabled:text-gray-400 flex items-center space-x-1\"\r\n                                >\r\n                                    <svg className={`h-4 w-4 ${loadingSpecialties ? 'animate-spin' : ''}`} xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\r\n                                    </svg>\r\n                                    <span>Tải lại</span>\r\n                                </button>\r\n                            </div>\r\n                            <div className=\"relative\">\r\n                                <select\r\n                                    name=\"specialtyId\"\r\n                                    value={formData.specialtyId}\r\n                                    onChange={handleInputChange}\r\n                                    disabled={loadingSpecialties}\r\n                                    className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.specialtyId ? 'border-red-500' : 'border-gray-300'\r\n                                        } ${loadingSpecialties ? 'bg-gray-100 cursor-not-allowed' : ''}`}\r\n                                >\r\n                                    <option value=\"\">\r\n                                        {loadingSpecialties ? 'Đang tải chuyên khoa...' : 'Chọn chuyên khoa'}\r\n                                    </option>\r\n                                    {!loadingSpecialties && specialties.map((specialty) => (\r\n                                        <option key={specialty.id} value={specialty.id}>\r\n                                            {specialty.specialtyName}\r\n                                        </option>\r\n                                    ))}\r\n                                </select>\r\n                                {loadingSpecialties && (\r\n                                    <div className=\"absolute right-3 top-1/2 transform -translate-y-1/2\">\r\n                                        <svg className=\"animate-spin h-4 w-4 text-gray-500\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                                            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                                            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                                        </svg>\r\n                                    </div>\r\n                                )}\r\n                            </div>\r\n                            {errors.specialtyId && <p className=\"mt-1 text-sm text-red-600\">{errors.specialtyId}</p>}\r\n                            {!loadingSpecialties && specialties.length === 0 && (\r\n                                <div className=\"mt-1 p-2 bg-yellow-50 border border-yellow-200 rounded-md\">\r\n                                    <p className=\"text-sm text-yellow-800\">\r\n                                        ⚠️ Không có chuyên khoa nào. Vui lòng thêm chuyên khoa trước khi tạo bác sĩ.\r\n                                    </p>\r\n                                </div>\r\n                            )}\r\n                        </div>\r\n\r\n                        {/* Số giấy phép hành nghề */}\r\n                        <div>\r\n                            <label className=\"block text-sm font-medium text-gray-700\">\r\n                                Số giấy phép hành nghề <span className=\"text-red-500\">*</span>\r\n                            </label>\r\n                            <input\r\n                                name=\"licenseNumber\"\r\n                                type=\"text\"\r\n                                value={formData.licenseNumber}\r\n                                onChange={handleInputChange}\r\n                                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.licenseNumber ? 'border-red-500' : 'border-gray-300'\r\n                                    }`}\r\n                                placeholder=\"Nhập số giấy phép\"\r\n                            />\r\n                            {errors.licenseNumber && <p className=\"mt-1 text-sm text-red-600\">{errors.licenseNumber}</p>}\r\n                        </div>\r\n\r\n                        {/* Bằng cấp */}\r\n                        <div>\r\n                            <label className=\"block text-sm font-medium text-gray-700\">Bằng cấp</label>\r\n                            <input\r\n                                name=\"degree\"\r\n                                type=\"text\"\r\n                                value={formData.degree}\r\n                                onChange={handleInputChange}\r\n                                className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\r\n                                placeholder=\"Nhập bằng cấp\"\r\n                            />\r\n                        </div>\r\n\r\n                        {/* Phí khám */}\r\n                        <div>\r\n                            <label className=\"block text-sm font-medium text-gray-700\">\r\n                                Phí khám (VNĐ) <span className=\"text-red-500\">*</span>\r\n                            </label>\r\n                            <input\r\n                                name=\"consultationFee\"\r\n                                type=\"number\"\r\n                                value={formData.consultationFee}\r\n                                onChange={handleInputChange}\r\n                                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.consultationFee ? 'border-red-500' : 'border-gray-300'\r\n                                    }`}\r\n                                placeholder=\"Nhập phí khám\"\r\n                                min=\"0\"\r\n                            />\r\n                            {errors.consultationFee && <p className=\"mt-1 text-sm text-red-600\">{errors.consultationFee}</p>}\r\n                        </div>\r\n\r\n                        {/* Số năm kinh nghiệm */}\r\n                        <div>\r\n                            <label className=\"block text-sm font-medium text-gray-700\">\r\n                                Số năm kinh nghiệm <span className=\"text-red-500\">*</span>\r\n                            </label>\r\n                            <input\r\n                                name=\"yearsOfExperience\"\r\n                                type=\"number\"\r\n                                value={formData.yearsOfExperience}\r\n                                onChange={handleInputChange}\r\n                                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${errors.yearsOfExperience ? 'border-red-500' : 'border-gray-300'\r\n                                    }`}\r\n                                placeholder=\"Nhập số năm kinh nghiệm\"\r\n                                min=\"0\"\r\n                            />\r\n                            {errors.yearsOfExperience && <p className=\"mt-1 text-sm text-red-600\">{errors.yearsOfExperience}</p>}\r\n                        </div>\r\n\r\n                        {/* Giới tính */}\r\n                        <div>\r\n                            <label className=\"block text-sm font-medium text-gray-700\">Giới tính</label>\r\n                            <select\r\n                                name=\"gender\"\r\n                                value={formData.gender}\r\n                                onChange={handleInputChange}\r\n                                className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\r\n                            >\r\n                                <option value=\"\">Chọn giới tính</option>\r\n                                <option value=\"0\">Nam</option>\r\n                                <option value=\"1\">Nữ</option>\r\n                                <option value=\"2\">Khác</option>\r\n                            </select>\r\n                        </div>\r\n\r\n                        {/* Trạng thái hoạt động */}\r\n                        <div>\r\n                            <label className=\"block text-sm font-medium text-gray-700\">Trạng thái</label>\r\n                            <div className=\"mt-2\">\r\n                                <label className=\"inline-flex items-center\">\r\n                                    <input\r\n                                        type=\"checkbox\"\r\n                                        name=\"isAvailable\"\r\n                                        checked={formData.isAvailable}\r\n                                        onChange={handleInputChange}\r\n                                        className=\"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50\"\r\n                                    />\r\n                                    <span className=\"ml-2 text-sm text-gray-700\">Đang hoạt động</span>\r\n                                </label>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* Tiểu sử */}\r\n                    <div>\r\n                        <label className=\"block text-sm font-medium text-gray-700\">Tiểu sử</label>\r\n                        <textarea\r\n                            name=\"bio\"\r\n                            value={formData.bio}\r\n                            onChange={handleInputChange}\r\n                            rows={3}\r\n                            className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\r\n                            placeholder=\"Nhập tiểu sử bác sĩ\"\r\n                        />\r\n                    </div>\r\n\r\n                    {/* Buttons */}\r\n                    <div className=\"flex justify-end space-x-3 pt-4\">\r\n                        <button\r\n                            type=\"button\"\r\n                            onClick={onClose}\r\n                            disabled={loading}\r\n                            className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 disabled:opacity-50\"\r\n                        >\r\n                            Hủy\r\n                        </button>\r\n                        <button\r\n                            type=\"submit\"\r\n                            disabled={loading || loadingSpecialties || specialties.length === 0}\r\n                            className=\"px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2\"\r\n                        >\r\n                            {loading && (\r\n                                <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                                    <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                                    <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                                </svg>\r\n                            )}\r\n                            <span>{loading ? 'Đang xử lý...' : (doctor ? 'Cập nhật' : 'Thêm')}</span>\r\n                        </button>\r\n                    </div>\r\n                </form>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AAGA;AACA;AACA;AANA;;;;;;AAeO,MAAM,cAAc,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAoB;IAChF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B,EAAE;IAC5E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,OAAO;QACP,OAAO;QACP,WAAW;QACX,UAAU;QACV,aAAa;QACb,eAAe;QACf,QAAQ;QACR,iBAAiB;QACjB,QAAQ;QACR,mBAAmB;QACnB,KAAK;QACL,QAAQ;QACR,aAAa;IACjB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,QAAQ;YACR;YACA,IAAI,QAAQ,CACZ,OAAO;gBACH,YAAY;oBACR,OAAO;oBACP,OAAO;oBACP,WAAW;oBACX,UAAU;oBACV,aAAa;oBACb,eAAe;oBACf,QAAQ;oBACR,iBAAiB;oBACjB,QAAQ;oBACR,mBAAmB;oBACnB,KAAK;oBACL,QAAQ;oBACR,aAAa;gBACjB;YACJ;YACA,UAAU,CAAC;QACf;IACJ,GAAG;QAAC;QAAQ;KAAO;IAEnB,MAAM,mBAAmB;QACrB,sBAAsB;QACtB,IAAI;YACA,MAAM,WAAW,MAAM,CAAA,GAAA,mIAAA,CAAA,iBAAc,AAAD,EAAE,GAAG;YACzC,IAAI,SAAS,IAAI,KAAK,OAAO,SAAS,MAAM,EAAE;gBAC1C,eAAe,SAAS,MAAM,CAAC,KAAK,IAAI,EAAE;YAC9C,OAAO;gBACH,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,uCAAuC;oBAC/C,UAAU;oBACV,OAAO;wBACH,YAAY;wBACZ,OAAO;oBACX;gBACJ;YACJ;QACJ,EAAE,OAAO,OAAO;YACZ,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,mCAAmC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB,EAAE;gBAC1G,UAAU;gBACV,OAAO;oBACH,YAAY;oBACZ,OAAO;gBACX;YACJ;QACJ,SAAU;YACN,sBAAsB;QAC1B;IACJ;IAEA,MAAM,oBAAoB,CAAC;QACvB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM;QACtC,YAAY,CAAA,OAAQ,CAAC;gBACjB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,aAAa,AAAC,EAAE,MAAM,CAAsB,OAAO,GAAG;YAC3E,CAAC;QAED,IAAI,MAAM,CAAC,KAAK,EAAE;YACd,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,KAAK,EAAE;gBAAG,CAAC;QAC9C;IACJ;IAEA,MAAM,eAAe;QACjB,MAAM,YAAoC,CAAC;QAC3C,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YACxB,UAAU,KAAK,GAAG;QACtB,OAAO,IAAI,CAAC,6BAA6B,IAAI,CAAC,SAAS,KAAK,GAAG;YAC3D,UAAU,KAAK,GAAG;QACtB;QACA,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YACxB,UAAU,KAAK,GAAG;QACtB,OAAO,IAAI,CAAC,iBAAiB,IAAI,CAAC,SAAS,KAAK,GAAG;YAC/C,UAAU,KAAK,GAAG;QACtB;QACA,IAAI,CAAC,SAAS,SAAS,CAAC,IAAI,IAAI;YAC5B,UAAU,SAAS,GAAG;QAC1B;QACA,IAAI,CAAC,SAAS,QAAQ,CAAC,IAAI,IAAI;YAC3B,UAAU,QAAQ,GAAG;QACzB;QACA,IAAI,CAAC,SAAS,WAAW,CAAC,IAAI,IAAI;YAC9B,UAAU,WAAW,GAAG;QAC5B,OAAO,IAAI,YAAY,MAAM,KAAK,GAAG;YACjC,UAAU,WAAW,GAAG;QAC5B;QACA,IAAI,CAAC,SAAS,aAAa,CAAC,IAAI,IAAI;YAChC,UAAU,aAAa,GAAG;QAC9B;QACA,IAAI,CAAC,SAAS,eAAe,CAAC,IAAI,IAAI;YAClC,UAAU,eAAe,GAAG;QAChC,OAAO,IAAI,MAAM,OAAO,SAAS,eAAe,MAAM,OAAO,SAAS,eAAe,IAAI,GAAG;YACxF,UAAU,eAAe,GAAG;QAChC;QACA,IAAI,CAAC,SAAS,iBAAiB,CAAC,IAAI,IAAI;YACpC,UAAU,iBAAiB,GAAG;QAClC,OAAO,IAAI,MAAM,OAAO,SAAS,iBAAiB,MAAM,OAAO,SAAS,iBAAiB,IAAI,GAAG;YAC5F,UAAU,iBAAiB,GAAG;QAClC;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC7C;IAEA,MAAM,eAAe,OAAO;QACxB,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACjB;QACJ;QAEA,IAAI,YAAY,MAAM,KAAK,GAAG;YAC1B,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,qDAAqD;gBAC7D,UAAU;gBACV,OAAO;oBACH,YAAY;oBACZ,OAAO;gBACX;YACJ;YACA;QACJ;QAEA,WAAW;QAEX,IAAI;YACA,MAAM,UAAiC;gBACnC,OAAO,SAAS,KAAK;gBACrB,OAAO,SAAS,KAAK;gBACrB,WAAW,SAAS,SAAS;gBAC7B,UAAU,SAAS,QAAQ;gBAC3B,aAAa,OAAO,SAAS,WAAW;gBACxC,eAAe,SAAS,aAAa;gBACrC,QAAQ,SAAS,MAAM,IAAI;gBAC3B,iBAAiB,OAAO,SAAS,eAAe;gBAChD,QAAQ,SAAS,MAAM,IAAc;gBACrC,mBAAmB,OAAO,SAAS,iBAAiB;gBACpD,KAAK,SAAS,GAAG,IAAI;gBACrB,QAAQ,SAAS,MAAM,IAAI;YAC/B;YAEA,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD,EAAE;YAEpC,IAAI,SAAS,IAAI,KAAK,OAAO,SAAS,MAAM,EAAE;gBAC1C,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC,8BAA8B;oBACxC,UAAU;oBACV,OAAO;wBACH,YAAY;wBACZ,OAAO;oBACX;gBACJ;gBACA;gBACA;YACJ,OAAO;gBACH,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,SAAS,OAAO,IAAI,2CAA2C,EAAE;oBAC9E,UAAU;oBACV,OAAO;wBACH,YAAY;wBACZ,OAAO;oBACX;gBACJ;YACJ;QACJ,EAAE,OAAO,OAAY;YACjB,QAAQ,KAAK,CAAC,0BAA0B;YAExC,IAAI,eAAe;YACnB,IAAI,OAAO,SAAS;gBAChB,eAAe,CAAC,EAAE,EAAE,MAAM,OAAO,EAAE;YACvC;YAEA,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,cAAc;gBACtB,UAAU;gBACV,OAAO;oBACH,YAAY;oBACZ,OAAO;gBACX;YACJ;QACJ,SAAU;YACN,WAAW;QACf;IACJ;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACI,8OAAC;QAAI,WAAU;kBACX,cAAA,8OAAC;YAAI,WAAU;;8BACX,8OAAC;oBAAG,WAAU;8BACT,SAAS,qBAAqB;;;;;;8BAEnC,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACpC,8OAAC;4BAAI,WAAU;;8CAEX,8OAAC;;sDACG,8OAAC;4CAAM,WAAU;;gDAA0C;8DACnD,8OAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAEvC,8OAAC;4CACG,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,SAAS;4CACzB,UAAU;4CACV,WAAW,CAAC,qHAAqH,EAAE,OAAO,SAAS,GAAG,mBAAmB,mBACnK;4CACN,aAAY;;;;;;wCAEf,OAAO,SAAS,kBAAI,8OAAC;4CAAE,WAAU;sDAA6B,OAAO,SAAS;;;;;;;;;;;;8CAInF,8OAAC;;sDACG,8OAAC;4CAAM,WAAU;;gDAA0C;8DACpD,8OAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAEtC,8OAAC;4CACG,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,QAAQ;4CACxB,UAAU;4CACV,WAAW,CAAC,qHAAqH,EAAE,OAAO,QAAQ,GAAG,mBAAmB,mBAClK;4CACN,aAAY;;;;;;wCAEf,OAAO,QAAQ,kBAAI,8OAAC;4CAAE,WAAU;sDAA6B,OAAO,QAAQ;;;;;;;;;;;;8CAIjF,8OAAC;;sDACG,8OAAC;4CAAM,WAAU;;gDAA0C;8DACjD,8OAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAEzC,8OAAC;4CACG,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,KAAK;4CACrB,UAAU;4CACV,WAAW,CAAC,qHAAqH,EAAE,OAAO,KAAK,GAAG,mBAAmB,mBAC/J;4CACN,aAAY;;;;;;wCAEf,OAAO,KAAK,kBAAI,8OAAC;4CAAE,WAAU;sDAA6B,OAAO,KAAK;;;;;;;;;;;;8CAI3E,8OAAC;;sDACG,8OAAC;4CAAM,WAAU;;gDAA0C;8DACzC,8OAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAEjD,8OAAC;4CACG,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,KAAK;4CACrB,UAAU;4CACV,WAAW,CAAC,qHAAqH,EAAE,OAAO,KAAK,GAAG,mBAAmB,mBAC/J;4CACN,aAAY;;;;;;wCAEf,OAAO,KAAK,kBAAI,8OAAC;4CAAE,WAAU;sDAA6B,OAAO,KAAK;;;;;;;;;;;;;;;;;;sCAI/E,8OAAC;4BAAI,WAAU;;8CAEX,8OAAC;;sDACG,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;oDAAM,WAAU;;wDAA0C;sEAC3C,8OAAC;4DAAK,WAAU;sEAAe;;;;;;;;;;;;8DAE/C,8OAAC;oDACG,MAAK;oDACL,SAAS;oDACT,UAAU;oDACV,WAAU;;sEAEV,8OAAC;4DAAI,WAAW,CAAC,QAAQ,EAAE,qBAAqB,iBAAiB,IAAI;4DAAE,OAAM;4DAA6B,MAAK;4DAAO,SAAQ;4DAAY,QAAO;sEAC7I,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;sEAEzE,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;sDAGd,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;oDACG,MAAK;oDACL,OAAO,SAAS,WAAW;oDAC3B,UAAU;oDACV,UAAU;oDACV,WAAW,CAAC,qHAAqH,EAAE,OAAO,WAAW,GAAG,mBAAmB,kBACtK,CAAC,EAAE,qBAAqB,mCAAmC,IAAI;;sEAEpE,8OAAC;4DAAO,OAAM;sEACT,qBAAqB,4BAA4B;;;;;;wDAErD,CAAC,sBAAsB,YAAY,GAAG,CAAC,CAAC,0BACrC,8OAAC;gEAA0B,OAAO,UAAU,EAAE;0EACzC,UAAU,aAAa;+DADf,UAAU,EAAE;;;;;;;;;;;gDAKhC,oCACG,8OAAC;oDAAI,WAAU;8DACX,cAAA,8OAAC;wDAAI,WAAU;wDAAqC,OAAM;wDAA6B,MAAK;wDAAO,SAAQ;;0EACvG,8OAAC;gEAAO,WAAU;gEAAa,IAAG;gEAAK,IAAG;gEAAK,GAAE;gEAAK,QAAO;gEAAe,aAAY;;;;;;0EACxF,8OAAC;gEAAK,WAAU;gEAAa,MAAK;gEAAe,GAAE;;;;;;;;;;;;;;;;;;;;;;;wCAKlE,OAAO,WAAW,kBAAI,8OAAC;4CAAE,WAAU;sDAA6B,OAAO,WAAW;;;;;;wCAClF,CAAC,sBAAsB,YAAY,MAAM,KAAK,mBAC3C,8OAAC;4CAAI,WAAU;sDACX,cAAA,8OAAC;gDAAE,WAAU;0DAA0B;;;;;;;;;;;;;;;;;8CAQnD,8OAAC;;sDACG,8OAAC;4CAAM,WAAU;;gDAA0C;8DAChC,8OAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAE1D,8OAAC;4CACG,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,aAAa;4CAC7B,UAAU;4CACV,WAAW,CAAC,qHAAqH,EAAE,OAAO,aAAa,GAAG,mBAAmB,mBACvK;4CACN,aAAY;;;;;;wCAEf,OAAO,aAAa,kBAAI,8OAAC;4CAAE,WAAU;sDAA6B,OAAO,aAAa;;;;;;;;;;;;8CAI3F,8OAAC;;sDACG,8OAAC;4CAAM,WAAU;sDAA0C;;;;;;sDAC3D,8OAAC;4CACG,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,MAAM;4CACtB,UAAU;4CACV,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAKpB,8OAAC;;sDACG,8OAAC;4CAAM,WAAU;;gDAA0C;8DACxC,8OAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAElD,8OAAC;4CACG,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,eAAe;4CAC/B,UAAU;4CACV,WAAW,CAAC,qHAAqH,EAAE,OAAO,eAAe,GAAG,mBAAmB,mBACzK;4CACN,aAAY;4CACZ,KAAI;;;;;;wCAEP,OAAO,eAAe,kBAAI,8OAAC;4CAAE,WAAU;sDAA6B,OAAO,eAAe;;;;;;;;;;;;8CAI/F,8OAAC;;sDACG,8OAAC;4CAAM,WAAU;;gDAA0C;8DACpC,8OAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAEtD,8OAAC;4CACG,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,iBAAiB;4CACjC,UAAU;4CACV,WAAW,CAAC,qHAAqH,EAAE,OAAO,iBAAiB,GAAG,mBAAmB,mBAC3K;4CACN,aAAY;4CACZ,KAAI;;;;;;wCAEP,OAAO,iBAAiB,kBAAI,8OAAC;4CAAE,WAAU;sDAA6B,OAAO,iBAAiB;;;;;;;;;;;;8CAInG,8OAAC;;sDACG,8OAAC;4CAAM,WAAU;sDAA0C;;;;;;sDAC3D,8OAAC;4CACG,MAAK;4CACL,OAAO,SAAS,MAAM;4CACtB,UAAU;4CACV,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAG;;;;;;8DACjB,8OAAC;oDAAO,OAAM;8DAAI;;;;;;8DAClB,8OAAC;oDAAO,OAAM;8DAAI;;;;;;8DAClB,8OAAC;oDAAO,OAAM;8DAAI;;;;;;;;;;;;;;;;;;8CAK1B,8OAAC;;sDACG,8OAAC;4CAAM,WAAU;sDAA0C;;;;;;sDAC3D,8OAAC;4CAAI,WAAU;sDACX,cAAA,8OAAC;gDAAM,WAAU;;kEACb,8OAAC;wDACG,MAAK;wDACL,MAAK;wDACL,SAAS,SAAS,WAAW;wDAC7B,UAAU;wDACV,WAAU;;;;;;kEAEd,8OAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO7D,8OAAC;;8CACG,8OAAC;oCAAM,WAAU;8CAA0C;;;;;;8CAC3D,8OAAC;oCACG,MAAK;oCACL,OAAO,SAAS,GAAG;oCACnB,UAAU;oCACV,MAAM;oCACN,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKpB,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;oCACG,MAAK;oCACL,SAAS;oCACT,UAAU;oCACV,WAAU;8CACb;;;;;;8CAGD,8OAAC;oCACG,MAAK;oCACL,UAAU,WAAW,sBAAsB,YAAY,MAAM,KAAK;oCAClE,WAAU;;wCAET,yBACG,8OAAC;4CAAI,WAAU;4CAA6C,OAAM;4CAA6B,MAAK;4CAAO,SAAQ;;8DAC/G,8OAAC;oDAAO,WAAU;oDAAa,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAK,QAAO;oDAAe,aAAY;;;;;;8DACxF,8OAAC;oDAAK,WAAU;oDAAa,MAAK;oDAAe,GAAE;;;;;;;;;;;;sDAG3D,8OAAC;sDAAM,UAAU,kBAAmB,SAAS,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtF", "debugId": null}}, {"offset": {"line": 1624, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/hooks/useManagerDoctors.ts"], "sourcesContent": ["import { useState, useEffect } from 'react';\r\nimport { DoctorDetailResponse} from '@/types/doctor';\r\nimport { getDoctors, SearchDoctorsParams } from '@/services/doctorService';\r\n\r\nexport const useManagerDoctors = () => {\r\n    const [doctors, setDoctors] = useState<DoctorDetailResponse[]>([]);\r\n    const [loading, setLoading] = useState(false);\r\n    const [error, setError] = useState<string | null>(null);\r\n    const [currentPage, setCurrentPage] = useState(1);\r\n    const [totalPages, setTotalPages] = useState(0);\r\n    const [totalElements, setTotalElements] = useState(0);\r\n    const [pageSize] = useState(10);\r\n\r\n    const searchDoctors = async (params: SearchDoctorsParams = {}) => {\r\n        setLoading(true);\r\n        setError(null);\r\n        \r\n        console.log('🔍 Debug - Hook searchDoctors called with params:', params);\r\n        \r\n        try {\r\n            const response = await getDoctors({\r\n                ...params,\r\n                page: currentPage,\r\n                pageSize: pageSize\r\n            });\r\n            \r\n            console.log('🔍 Debug - Hook received response:', response);\r\n            \r\n            if (response.code === 200 && response.result) {\r\n                setDoctors(response.result.items);\r\n                setTotalPages(response.result.totalPages);\r\n                setTotalElements(response.result.totalElements);\r\n            } else {\r\n                setError(response.message || 'Failed to fetch doctors');\r\n            }\r\n        } catch (err) {\r\n            console.error('🔍 Debug - Hook error:', err);\r\n            setError(err instanceof Error ? err.message : 'An error occurred');\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        searchDoctors();\r\n    }, [currentPage]);\r\n\r\n    return {\r\n        doctors,\r\n        loading,\r\n        error,\r\n        currentPage,\r\n        totalPages,\r\n        totalElements,\r\n        searchDoctors,\r\n        setCurrentPage\r\n    };\r\n}; "], "names": [], "mappings": ";;;AAAA;AAEA;;;AAEO,MAAM,oBAAoB;IAC7B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,EAAE;IACjE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE5B,MAAM,gBAAgB,OAAO,SAA8B,CAAC,CAAC;QACzD,WAAW;QACX,SAAS;QAET,QAAQ,GAAG,CAAC,qDAAqD;QAEjE,IAAI;YACA,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,aAAU,AAAD,EAAE;gBAC9B,GAAG,MAAM;gBACT,MAAM;gBACN,UAAU;YACd;YAEA,QAAQ,GAAG,CAAC,sCAAsC;YAElD,IAAI,SAAS,IAAI,KAAK,OAAO,SAAS,MAAM,EAAE;gBAC1C,WAAW,SAAS,MAAM,CAAC,KAAK;gBAChC,cAAc,SAAS,MAAM,CAAC,UAAU;gBACxC,iBAAiB,SAAS,MAAM,CAAC,aAAa;YAClD,OAAO;gBACH,SAAS,SAAS,OAAO,IAAI;YACjC;QACJ,EAAE,OAAO,KAAK;YACV,QAAQ,KAAK,CAAC,0BAA0B;YACxC,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAClD,SAAU;YACN,WAAW;QACf;IACJ;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN;IACJ,GAAG;QAAC;KAAY;IAEhB,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACJ;AACJ", "debugId": null}}, {"offset": {"line": 1686, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/doctors/PageNavigation.tsx"], "sourcesContent": ["'use client';\r\nimport React from 'react';\r\n\r\ninterface PageNavigationProps {\r\n    currentPage: number;\r\n    totalPages: number;\r\n    setCurrentPage: (page: number) => void;\r\n}\r\n\r\nconst PageNavigation: React.FC<PageNavigationProps> = ({ currentPage, totalPages, setCurrentPage }) => {\r\n    const maxPagesToShow = 5; // Số trang tối đa hiển thị\r\n    const startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));\r\n    const endPage = Math.min(totalPages, startPage + maxPagesToShow - 1);\r\n\r\n    const pages = Array.from({ length: endPage - startPage + 1 }, (_, i) => startPage + i);\r\n\r\n    return (\r\n        <div className=\"mt-12 flex justify-center\">\r\n            <nav className=\"inline-flex rounded-lg shadow-md bg-white\">\r\n                {/* Previous Button */}\r\n                <button\r\n                    onClick={() => setCurrentPage(currentPage - 1)}\r\n                    disabled={currentPage === 1}\r\n                    className={`py-3 px-5 border border-gray-200 bg-white rounded-l-lg text-gray-700 font-medium transition duration-200 ${currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'\r\n                        }`}\r\n                >\r\n                    Trước\r\n                </button>\r\n\r\n                {/* Page Numbers */}\r\n                {pages.map((page) => (\r\n                    <button\r\n                        key={page}\r\n                        onClick={() => setCurrentPage(page)}\r\n                        className={`py-3 px-5 border-t border-b border-gray-200 font-medium transition duration-200 ${page === currentPage ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50'\r\n                            }`}\r\n                    >\r\n                        {page}\r\n                    </button>\r\n                ))}\r\n\r\n                {/* Next Button */}\r\n                <button\r\n                    onClick={() => setCurrentPage(currentPage + 1)}\r\n                    disabled={currentPage === totalPages}\r\n                    className={`py-3 px-5 border border-gray-200 bg-white rounded-r-lg text-gray-700 font-medium transition duration-200 ${currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'\r\n                        }`}\r\n                >\r\n                    Tiếp\r\n                </button>\r\n            </nav>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default PageNavigation;"], "names": [], "mappings": ";;;;AAAA;;AASA,MAAM,iBAAgD,CAAC,EAAE,WAAW,EAAE,UAAU,EAAE,cAAc,EAAE;IAC9F,MAAM,iBAAiB,GAAG,2BAA2B;IACrD,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,cAAc,KAAK,KAAK,CAAC,iBAAiB;IACxE,MAAM,UAAU,KAAK,GAAG,CAAC,YAAY,YAAY,iBAAiB;IAElE,MAAM,QAAQ,MAAM,IAAI,CAAC;QAAE,QAAQ,UAAU,YAAY;IAAE,GAAG,CAAC,GAAG,IAAM,YAAY;IAEpF,qBACI,8OAAC;QAAI,WAAU;kBACX,cAAA,8OAAC;YAAI,WAAU;;8BAEX,8OAAC;oBACG,SAAS,IAAM,eAAe,cAAc;oBAC5C,UAAU,gBAAgB;oBAC1B,WAAW,CAAC,yGAAyG,EAAE,gBAAgB,IAAI,kCAAkC,oBACvK;8BACT;;;;;;gBAKA,MAAM,GAAG,CAAC,CAAC,qBACR,8OAAC;wBAEG,SAAS,IAAM,eAAe;wBAC9B,WAAW,CAAC,gFAAgF,EAAE,SAAS,cAAc,2BAA2B,2CAC1I;kCAEL;uBALI;;;;;8BAUb,8OAAC;oBACG,SAAS,IAAM,eAAe,cAAc;oBAC5C,UAAU,gBAAgB;oBAC1B,WAAW,CAAC,yGAAyG,EAAE,gBAAgB,aAAa,kCAAkC,oBAChL;8BACT;;;;;;;;;;;;;;;;;AAMjB;uCAEe", "debugId": null}}, {"offset": {"line": 1752, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/manager/doctors/DoctorFilters.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Search, Filter } from 'lucide-react';\r\nimport { Gender } from '@/types/doctor';\r\n\r\ninterface DoctorManagerFiltersProps {\r\n    searchTerm: string;\r\n    selectedDepartment: string;\r\n    selectedGender?: Gender;\r\n    isAvailable?: boolean;\r\n    departments: string[];\r\n    onSearchChange: (value: string) => void;\r\n    onDepartmentChange: (value: string) => void;\r\n    onGenderChange: (value?: Gender) => void;\r\n    onAvailabilityChange: (value?: boolean) => void;\r\n}\r\n\r\nconst DoctorFilters = ({\r\n    searchTerm,\r\n    selectedDepartment,\r\n    selectedGender,\r\n    isAvailable,\r\n    departments,\r\n    onSearchChange,\r\n    onDepartmentChange,\r\n    onGenderChange,\r\n    onAvailabilityChange\r\n}: DoctorManagerFiltersProps) => {\r\n    return (\r\n        <div className=\"bg-white rounded-lg shadow-sm border p-4\">\r\n            <div className=\"flex flex-col md:flex-row gap-4\">\r\n                <div className=\"flex-1\">\r\n                    <div className=\"relative\">\r\n                        <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\r\n                        <input\r\n                            type=\"text\"\r\n                            placeholder=\"Tìm kiếm bác sĩ...\"\r\n                            value={searchTerm}\r\n                            onChange={(e) => onSearchChange(e.target.value)}\r\n                            className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900\"\r\n                        />\r\n                    </div>\r\n                </div>\r\n                <div className=\"flex items-center space-x-2\">\r\n                    <Filter className=\"w-4 h-4 text-gray-400\" />\r\n                    <select\r\n                        value={selectedDepartment}\r\n                        onChange={(e) => onDepartmentChange(e.target.value)}\r\n                        className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900\"\r\n                    >\r\n                        {departments.map((dept) => (\r\n                            <option key={dept} value={dept === 'Tất cả' ? 'all' : dept}>\r\n                                {dept}\r\n                            </option>\r\n                        ))}\r\n                    </select>\r\n                </div>\r\n            </div>\r\n\r\n            <div className=\"flex flex-wrap gap-4 mt-4\">\r\n                <div className=\"flex items-center gap-2\">\r\n                    <label className=\"text-sm font-medium text-gray-700\">Giới tính:</label>\r\n                    <select\r\n                        value={selectedGender || ''}\r\n                        onChange={(e) => onGenderChange(e.target.value ? e.target.value as Gender : undefined)}\r\n                        className=\"border border-gray-300 rounded-md px-3 py-1 text-sm\"\r\n                    >\r\n                        <option value=\"\">Tất cả</option>\r\n                        <option value=\"Male\">Nam</option>\r\n                        <option value=\"Female\">Nữ</option>\r\n                        <option value=\"Other\">Khác</option>\r\n                    </select>\r\n                </div>\r\n\r\n                <div className=\"flex items-center gap-2\">\r\n                    <label className=\"text-sm font-medium text-gray-700\">Trạng thái:</label>\r\n                    <select\r\n                        value={isAvailable === undefined ? '' : isAvailable.toString()}\r\n                        onChange={(e) => onAvailabilityChange(e.target.value === '' ? undefined : e.target.value === 'true')}\r\n                        className=\"border border-gray-300 rounded-md px-3 py-1 text-sm\"\r\n                    >\r\n                        <option value=\"\">Tất cả</option>\r\n                        <option value=\"true\">Có sẵn</option>\r\n                        <option value=\"false\">Nghỉ</option>\r\n                    </select>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default DoctorFilters;\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AAiBA,MAAM,gBAAgB,CAAC,EACnB,UAAU,EACV,kBAAkB,EAClB,cAAc,EACd,WAAW,EACX,WAAW,EACX,cAAc,EACd,kBAAkB,EAClB,cAAc,EACd,oBAAoB,EACI;IACxB,qBACI,8OAAC;QAAI,WAAU;;0BACX,8OAAC;gBAAI,WAAU;;kCACX,8OAAC;wBAAI,WAAU;kCACX,cAAA,8OAAC;4BAAI,WAAU;;8CACX,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCACG,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,WAAU;;;;;;;;;;;;;;;;;kCAItB,8OAAC;wBAAI,WAAU;;0CACX,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCACG,OAAO;gCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;gCAClD,WAAU;0CAET,YAAY,GAAG,CAAC,CAAC,qBACd,8OAAC;wCAAkB,OAAO,SAAS,WAAW,QAAQ;kDACjD;uCADQ;;;;;;;;;;;;;;;;;;;;;;0BAQ7B,8OAAC;gBAAI,WAAU;;kCACX,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCAAM,WAAU;0CAAoC;;;;;;0CACrD,8OAAC;gCACG,OAAO,kBAAkB;gCACzB,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK,GAAG,EAAE,MAAM,CAAC,KAAK,GAAa;gCAC5E,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAG;;;;;;kDACjB,8OAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,8OAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,8OAAC;wCAAO,OAAM;kDAAQ;;;;;;;;;;;;;;;;;;kCAI9B,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCAAM,WAAU;0CAAoC;;;;;;0CACrD,8OAAC;gCACG,OAAO,gBAAgB,YAAY,KAAK,YAAY,QAAQ;gCAC5D,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK,KAAK,KAAK,YAAY,EAAE,MAAM,CAAC,KAAK,KAAK;gCAC7F,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAG;;;;;;kDACjB,8OAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,8OAAC;wCAAO,OAAM;kDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM9C;uCAEe", "debugId": null}}, {"offset": {"line": 1976, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/components/manager/doctors/DoctorsManagement.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useState } from 'react';\r\nimport { Plus, Upload } from 'lucide-react';\r\nimport { DoctorC<PERSON><PERSON>Request, DoctorDetailResponse, DoctorUpdateRequest, Gender } from '@/types/doctor';\r\nimport { SpecialtyDetailResponse } from '@/types/specialty';\r\nimport { DoctorTable } from './DoctorTable';\r\nimport { DoctorModal } from './DoctorModal';\r\nimport { ScheduleModal } from './ScheduleModal';\r\nimport { UploadScheduleModal } from './UploadScheduleModal';\r\nimport { createDoctor, deleteDoctor, importDoctorSchedules, updateDoctor } from '@/services/doctorService';\r\nimport toast from 'react-hot-toast';\r\nimport { useManagerDoctors } from '@/hooks/useManagerDoctors';\r\nimport PageNavigation from '@/components/doctors/PageNavigation';\r\nimport DoctorFilters from './DoctorFilters';\r\n\r\ninterface DoctorsManagementProps {\r\n    specialties: SpecialtyDetailResponse[];\r\n}\r\n\r\nexport const DoctorsManagement = ({ specialties }: DoctorsManagementProps) => {\r\n    const [searchTerm, setSearchTerm] = useState('');\r\n    const [selectedDepartment, setSelectedDepartment] = useState('all');\r\n    const [selectedGender, setSelectedGender] = useState<Gender | undefined>(undefined);\r\n    const [isAvailable, setIsAvailable] = useState<boolean | undefined>(undefined);\r\n    const [showModal, setShowModal] = useState(false);\r\n    const [showScheduleModal, setScheduleModal] = useState(false);\r\n    const [selectedDoctor, setSelectedDoctor] = useState<DoctorDetailResponse | null>(null);\r\n    const [showUploadModal, setShowUploadModal] = useState(false);\r\n\r\n    const {\r\n        doctors,\r\n        currentPage,\r\n        totalPages,\r\n        setCurrentPage,\r\n        searchDoctors\r\n    } = useManagerDoctors();\r\n\r\n    const filteredDoctors = doctors.filter(doctor => {\r\n        const matchesSearch = doctor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n            doctor.department.toLowerCase().includes(searchTerm.toLowerCase());\r\n        const matchesDepartment = selectedDepartment === 'all' || doctor.department === selectedDepartment;\r\n        return matchesSearch && matchesDepartment;\r\n    });\r\n\r\n    const handleView = (doctor: DoctorDetailResponse) => {\r\n        setSelectedDoctor(doctor);\r\n        setShowModal(true);\r\n    };\r\n\r\n    const handleEdit = (doctor: DoctorDetailResponse) => {\r\n        setSelectedDoctor(doctor);\r\n        setShowModal(true);\r\n    };\r\n\r\n    const handleSchedule = (doctor: DoctorDetailResponse) => {\r\n        setSelectedDoctor(doctor);\r\n        setScheduleModal(true);\r\n    };\r\n\r\n    const handleDelete = async (id: number) => {\r\n        if (confirm('Bạn có chắc chắn muốn xóa bác sĩ này?')) {\r\n            try {\r\n                await deleteDoctor(id);\r\n                toast.success('Xóa bác sĩ thành công!');\r\n                setShowModal(false);\r\n                setSelectedDoctor(null);\r\n                searchDoctors(getSearchParams());\r\n            } catch (error) {\r\n                toast.error('Đã xảy ra lỗi khi xử lý!');\r\n                console.error(error);\r\n            }\r\n        }\r\n    };\r\n\r\n    const handleModalSuccess = () => {\r\n        // Refresh doctors list here if needed\r\n        console.log('Doctor created successfully');\r\n        setShowModal(false);\r\n        setSelectedDoctor(null);\r\n    };\r\n\r\n    const handleModalClose = () => {\r\n        setShowModal(false);\r\n        setSelectedDoctor(null);\r\n    };\r\n\r\n    const handleScheduleModalClose = () => {\r\n        setScheduleModal(false);\r\n        setSelectedDoctor(null);\r\n    };\r\n\r\n    const handlePageChange = (page: number) => {\r\n        setCurrentPage(page);\r\n        window.scrollTo({ top: 0, behavior: 'smooth' });\r\n    };\r\n\r\n    return (\r\n        <div className=\"space-y-6\">\r\n            <div className=\"flex justify-between items-center\">\r\n                <h2 className=\"text-2xl font-bold text-gray-900\">Quản lý Bác sĩ</h2>\r\n                <div className=\"flex space-x-2 ml-auto\">\r\n                    <button\r\n                        onClick={() => setShowUploadModal(true)}\r\n                        className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2\"\r\n                    >\r\n                        <Upload className=\"w-4 h-4\" />\r\n                        <span>Tải lên lịch làm việc</span>\r\n                    </button>\r\n                    <button\r\n                        onClick={() => setShowModal(true)}\r\n                        className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2\"\r\n                    >\r\n                        <Plus className=\"w-4 h-4\" />\r\n                        <span>Thêm Bác sĩ</span>\r\n                    </button>\r\n                </div>\r\n            </div>\r\n\r\n            <DoctorFilters\r\n                searchTerm={searchTerm}\r\n                selectedDepartment={selectedDepartment}\r\n                selectedGender={selectedGender}\r\n                isAvailable={isAvailable}\r\n                departments={departments}\r\n                onSearchChange={setSearchTerm}\r\n                onDepartmentChange={setSelectedDepartment}\r\n                onGenderChange={setSelectedGender}\r\n                onAvailabilityChange={setIsAvailable}\r\n            />\r\n\r\n            <DoctorTable\r\n                doctors={doctors}\r\n                onView={handleView}\r\n                onEdit={handleEdit}\r\n                onDelete={handleDelete}\r\n                onSchedule={handleSchedule}\r\n            />\r\n\r\n            {totalPages > 1 && (\r\n                <PageNavigation\r\n                    currentPage={currentPage}\r\n                    totalPages={totalPages}\r\n                    setCurrentPage={handlePageChange}\r\n                />\r\n            )}\r\n\r\n            <DoctorModal\r\n                isOpen={showModal}\r\n                doctor={selectedDoctor}\r\n                onClose={handleModalClose}\r\n                onSuccess={handleModalSuccess}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAGA;AACA;AAGA;AACA;AACA;AACA;AACA;AAdA;;;;;;;;;;;AAoBO,MAAM,oBAAoB,CAAC,EAAE,WAAW,EAA0B;IACrE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IACzE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACpE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,mBAAmB,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+B;IAClF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,EACF,OAAO,EACP,WAAW,EACX,UAAU,EACV,cAAc,EACd,aAAa,EAChB,GAAG,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD;IAEpB,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA;QACnC,MAAM,gBAAgB,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC3E,OAAO,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QACnE,MAAM,oBAAoB,uBAAuB,SAAS,OAAO,UAAU,KAAK;QAChF,OAAO,iBAAiB;IAC5B;IAEA,MAAM,aAAa,CAAC;QAChB,kBAAkB;QAClB,aAAa;IACjB;IAEA,MAAM,aAAa,CAAC;QAChB,kBAAkB;QAClB,aAAa;IACjB;IAEA,MAAM,iBAAiB,CAAC;QACpB,kBAAkB;QAClB,iBAAiB;IACrB;IAEA,MAAM,eAAe,OAAO;QACxB,IAAI,QAAQ,0CAA0C;YAClD,IAAI;gBACA,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD,EAAE;gBACnB,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;gBACd,aAAa;gBACb,kBAAkB;gBAClB,cAAc;YAClB,EAAE,OAAO,OAAO;gBACZ,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;gBACZ,QAAQ,KAAK,CAAC;YAClB;QACJ;IACJ;IAEA,MAAM,qBAAqB;QACvB,sCAAsC;QACtC,QAAQ,GAAG,CAAC;QACZ,aAAa;QACb,kBAAkB;IACtB;IAEA,MAAM,mBAAmB;QACrB,aAAa;QACb,kBAAkB;IACtB;IAEA,MAAM,2BAA2B;QAC7B,iBAAiB;QACjB,kBAAkB;IACtB;IAEA,MAAM,mBAAmB,CAAC;QACtB,eAAe;QACf,OAAO,QAAQ,CAAC;YAAE,KAAK;YAAG,UAAU;QAAS;IACjD;IAEA,qBACI,8OAAC;QAAI,WAAU;;0BACX,8OAAC;gBAAI,WAAU;;kCACX,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCACG,SAAS,IAAM,mBAAmB;gCAClC,WAAU;;kDAEV,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;kDAAK;;;;;;;;;;;;0CAEV,8OAAC;gCACG,SAAS,IAAM,aAAa;gCAC5B,WAAU;;kDAEV,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAKlB,8OAAC,yJAAA,CAAA,UAAa;gBACV,YAAY;gBACZ,oBAAoB;gBACpB,gBAAgB;gBAChB,aAAa;gBACb,aAAa;gBACb,gBAAgB;gBAChB,oBAAoB;gBACpB,gBAAgB;gBAChB,sBAAsB;;;;;;0BAG1B,8OAAC,uJAAA,CAAA,cAAW;gBACR,SAAS;gBACT,QAAQ;gBACR,QAAQ;gBACR,UAAU;gBACV,YAAY;;;;;;YAGf,aAAa,mBACV,8OAAC,+IAAA,CAAA,UAAc;gBACX,aAAa;gBACb,YAAY;gBACZ,gBAAgB;;;;;;0BAIxB,8OAAC,uJAAA,CAAA,cAAW;gBACR,QAAQ;gBACR,QAAQ;gBACR,SAAS;gBACT,WAAW;;;;;;;;;;;;AAI3B", "debugId": null}}, {"offset": {"line": 2198, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PRN232/PRN232-FE-Medical-Appointment/web-app/src/app/%28page%29/%28manager%29/manager/doctors/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useState } from 'react';\r\nimport { DoctorsManagement } from '@/components/manager/doctors/DoctorsManagement';\r\nimport { getDoctors } from '@/services/doctorService';\r\nimport { getSpecialties } from '@/services/specialtyService';\r\nimport { DoctorDetailResponse } from '@/types/doctor';\r\nimport { SpecialtyDetailResponse } from '@/types/specialty';\r\n\r\nexport default function DoctorsPage() {\r\n    const [doctors, setDoctors] = useState<DoctorDetailResponse[]>([]);\r\n    const [specialties, setSpecialties] = useState<SpecialtyDetailResponse[]>([]);\r\n    const [loading, setLoading] = useState(true);\r\n\r\n    useEffect(() => {\r\n        const fetchData = async () => {\r\n            try {\r\n                const [doctorRes, specialtyRes] = await Promise.all([\r\n                    getDoctors(),\r\n                    getSpecialties()\r\n                ]);\r\n\r\n                setDoctors(doctorRes.result?.items || []);\r\n                setSpecialties(specialtyRes.result?.items || []);\r\n            } catch (err) {\r\n                console.error('Error loading doctors or specialties:', err);\r\n            } finally {\r\n                setLoading(false);\r\n            }\r\n        };\r\n\r\n        fetchData();\r\n    }, []);\r\n\r\n    if (loading) {\r\n        return <div className=\"p-4 text-gray-600\">Đang tải danh sách bác sĩ...</div>;\r\n    }\r\n\r\n    return <DoctorsManagement doctors={doctors} specialties={specialties} />;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AASe,SAAS;IACpB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,EAAE;IACjE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B,EAAE;IAC5E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,YAAY;YACd,IAAI;gBACA,MAAM,CAAC,WAAW,aAAa,GAAG,MAAM,QAAQ,GAAG,CAAC;oBAChD,CAAA,GAAA,gIAAA,CAAA,aAAU,AAAD;oBACT,CAAA,GAAA,mIAAA,CAAA,iBAAc,AAAD;iBAChB;gBAED,WAAW,UAAU,MAAM,EAAE,SAAS,EAAE;gBACxC,eAAe,aAAa,MAAM,EAAE,SAAS,EAAE;YACnD,EAAE,OAAO,KAAK;gBACV,QAAQ,KAAK,CAAC,yCAAyC;YAC3D,SAAU;gBACN,WAAW;YACf;QACJ;QAEA;IACJ,GAAG,EAAE;IAEL,IAAI,SAAS;QACT,qBAAO,8OAAC;YAAI,WAAU;sBAAoB;;;;;;IAC9C;IAEA,qBAAO,8OAAC,6JAAA,CAAA,oBAAiB;QAAC,SAAS;QAAS,aAAa;;;;;;AAC7D", "debugId": null}}]}